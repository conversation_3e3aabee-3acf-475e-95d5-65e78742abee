# TypeScript to C Migration Checklist - Rounds 1-3: Common & Physics Modules

## Migration Summary
This document tracks the migration of TypeScript source files from `projects/scuba-physics/src/lib/` to C language implementation in `c_port/`.

## Completed Migrations

### 1. featureFlags.ts → featureFlags.c/h
**Source File:** `projects/scuba-physics/src/lib/common/featureFlags.ts`
**Target Files:** 
- `c_port/include/common/featureFlags.h`
- `c_port/src/common/featureFlags.c`

**Source Functions Ported and Their C Counterparts:**
- `FeatureFlags` class → `FeatureFlags` struct
- `FeatureFlags.Instance` (static getter) → `FeatureFlags_Instance()` function
- Private singleton pattern → Static singleton implementation with static storage

**Migration Notes:**
- Converted TypeScript singleton class to C singleton pattern with static storage
- Maintained exact same functionality - placeholder for conditional features
- All original comments preserved verbatim

### 2. precision.ts → precision.c/h
**Source File:** `projects/scuba-physics/src/lib/common/precision.ts`
**Target Files:**
- `c_port/include/common/precision.h`
- `c_port/src/common/precision.c`

**Source Functions Ported and Their C Counterparts:**
- `Precision.fix(source)` → `Precision_fix(double source)`
- `Precision.roundTwoDecimals(source)` → `Precision_roundTwoDecimals(double source)`
- `Precision.round(source, digits = 0)` → `Precision_round(double source, int digits)`
- `Precision.floorTwoDecimals(source)` → `Precision_floorTwoDecimals(double source)`
- `Precision.floor(source, digits = 0)` → `Precision_floor(double source, int digits)`
- `Precision.ceilTwoDecimals(source)` → `Precision_ceilTwoDecimals(double source)`
- `Precision.ceil(source, digits = 0)` → `Precision_ceil(double source, int digits)`
- `Precision.ceilDistance(source, distance)` → `Precision_ceilDistance(double source, double distance)`
- `Precision.roundDistance(source, distance)` → `Precision_roundDistance(double source, double distance)`
- `Precision.floorDistance(source, distance)` → `Precision_floorDistance(double source, double distance)`
- `Precision.adapt()` (private) → `Precision_adapt()` (static function)
- `Precision.adaptDistance()` (private) → `Precision_adaptDistance()` (static function)

**Migration Notes:**
- Converted all static class methods to C functions with Precision_ prefix
- Used function pointers to replicate JavaScript Math function passing pattern
- Maintained exact precision calculations using pow() for digit-based operations
- All original comments preserved verbatim, including spelling and formatting
- Used double throughout as specified in precision contract

### 3. linearFunction.ts → linearFunction.c/h
**Source File:** `projects/scuba-physics/src/lib/common/linearFunction.ts`
**Target Files:**
- `c_port/include/common/linearFunction.h`
- `c_port/src/common/linearFunction.c`

**Source Functions Ported and Their C Counterparts:**
- `Range` interface → `Range` struct with `double start, end` fields
- `LinearFunction.speed(x, y)` → `LinearFunction_speed(Range x, Range y)`
- `LinearFunction.speedByXChange(yStart, yEnd, xChange)` → `LinearFunction_speedByXChange(double yStart, double yEnd, double xChange)`
- `LinearFunction.yValueAt(yStart, speed, xChange)` → `LinearFunction_yValueAt(double yStart, double speed, double xChange)`
- `LinearFunction.xValueAtAbsolute(x, y, yValue)` → `LinearFunction_xValueAtAbsolute(Range x, Range y, double yValue)`
- `LinearFunction.xValueAt(yStart, speed, yValue)` → `LinearFunction_xValueAt(double yStart, double speed, double yValue)`

**Migration Notes:**
- Converted TypeScript interface to C struct
- Converted all static class methods to C functions with LinearFunction_ prefix
- Maintained exact calculation logic and zero-division check (`speed === 0` → `speed == 0.0`)
- All original comments preserved verbatim, including comment formatting and content
- Used pass-by-value for Range struct as it's small (two doubles)

## Migration Quality Verification

### Precision Contract Compliance
- **Numeric Type**: All functions use `double` type as required
- **Zero Comparisons**: Used `== 0.0` for exact zero comparisons (appropriate for algorithm context)
- **Mathematical Operations**: All mathematical operations preserved exactly from TypeScript
- **Rounding Behavior**: Precision functions maintain exact same rounding/truncation/ceiling behavior

### Code Coverage Verification
- ✅ All classes/interfaces migrated
- ✅ All public methods migrated
- ✅ All private/static methods migrated  
- ✅ All constants migrated
- ✅ All comments migrated verbatim
- ✅ All export functionality mapped to appropriate C patterns

### Naming Consistency
- TypeScript class methods → C functions with Class_ prefix
- TypeScript interfaces → C structs
- All variable names maintained exactly
- Function parameters maintain same names and order

## Files Created
1. `c_port/include/common/featureFlags.h`
2. `c_port/src/common/featureFlags.c`
3. `c_port/include/common/precision.h`
4. `c_port/src/common/precision.c`
5. `c_port/include/common/linearFunction.h`
6. `c_port/src/common/linearFunction.c`
7. `c_port/include/common/binaryIntervalSearch.h`
8. `c_port/src/common/binaryIntervalSearch.c`

### 4. BinaryIntervalSearch.ts → binaryIntervalSearch.c/h
**Source File:** `projects/scuba-physics/src/lib/common/BinaryIntervalSearch.ts`
**Target Files:**
- `c_port/include/common/binaryIntervalSearch.h`
- `c_port/src/common/binaryIntervalSearch.c`

**Source Functions Ported and Their C Counterparts:**
- `Interval` interface → `Interval` struct with `double left, right` fields
- `SearchContext` interface → `SearchContext` struct with callback function pointers
  - `doWork: (newValue: number) => void` → `void (*doWork)(double newValue)`
  - `meetsCondition: () => boolean` → `int (*meetsCondition)(void)`
- `BinaryIntervalSearch.search(context)` → `BinaryIntervalSearch_search(SearchContext* context)`
- `BinaryIntervalSearch.searchInsideInterval()` (private) → `BinaryIntervalSearch_searchInsideInterval()`
- `BinaryIntervalSearch.findInitialLimit()` (private) → `BinaryIntervalSearch_findInitialLimit()`
- `BinaryIntervalSearch.minimalStep` (static readonly) → `minimalStep` (static const double)

**Migration Notes:**
- Converted TypeScript interfaces to C structs with exact field mappings
- Converted callback functions to C function pointers maintaining exact signature semantics
- Converted class methods to C functions with BinaryIntervalSearch_ prefix
- Maintained exact binary search algorithm logic including:
  - Identical loop conditions (`limits.right - limits.left > minimalStep`)
  - Identical middle point calculation with `round()` function
  - Identical boundary update logic
  - Identical initial limit finding algorithm
- Preserved all English comments verbatim, including Wikipedia reference link
- Used proper C error handling with fprintf/exit for consistency with TS exceptions
- All mathematical operations use double precision as specified in precision contract

## Round 3: Physics Module Migrations

### 5. Time.ts → time.c/h
**Source File:** `projects/scuba-physics/src/lib/physics/Time.ts`
**Target Files:**
- `c_port/include/physics/time.h`
- `c_port/src/physics/time.c`

**Source Functions Ported and Their C Counterparts:**
- `Time.oneSecond` (static readonly) → `Time_oneSecond` (const double)
- `Time.oneMinute` (static readonly) → `Time_oneMinute` (const double)
- `Time.oneHour` (static readonly) → `Time_oneHour` (const double)
- `Time.oneDay` (static readonly) → `Time_oneDay` (const double)
- `Time.safetyStopDuration` (static readonly) → `Time_safetyStopDuration` (const double)
- `Time.toSeconds(minutes)` → `Time_toSeconds(double minutes)`
- `Time.toMinutes(seconds)` → `Time_toMinutes(double seconds)`
- `Time.toHours(seconds)` → `Time_toHours(double seconds)`

**Migration Notes:**
- Converted all static readonly properties to const double constants with Time_ prefix
- Converted all static class methods to C functions with Time_ prefix
- Maintained exact calculation relationships: oneHour = oneMinute * 60, oneDay = oneHour * 24, safetyStopDuration = oneMinute * 3
- All original comments preserved verbatim
- Used double throughout as specified in precision contract
- All mathematical operations preserved exactly from TypeScript

### 6. pressure-converter.ts → pressureConverter.c/h
**Source File:** `projects/scuba-physics/src/lib/physics/pressure-converter.ts`
**Target Files:**
- `c_port/include/physics/pressureConverter.h`
- `c_port/src/physics/pressureConverter.c`

**Source Functions Ported and Their C Counterparts:**
- `Salinity` enum → `Salinity` typedef enum with identical values (fresh=1, brackish=2, salt=3)
- `Density.fresh` (static readonly) → `Density_fresh` (const double = 1000.0)
- `Density.brackish` (static readonly) → `Density_brackish` (const double = 1020.0)
- `Density.salt` (static readonly) → `Density_salt` (const double = 1030.0)
- `Gravity.standard` (static readonly) → `Gravity_standard` (const double = 9.80665)
- `PressureConverter.pascalToBar(pascals)` → `PressureConverter_pascalToBar(double pascals)`
- `PressureConverter.barToPascal(bars)` → `PressureConverter_barToPascal(double bars)`
- `AltitudePressure.standard` (static readonly) → `AltitudePressure_standard` (const double = 1.01325)
- `AltitudePressure.pressure(altitude)` → `AltitudePressure_pressure(double altitude)`
- `AltitudePressure.altitude(pressure)` → `AltitudePressure_altitude(double pressure)`
- Private static constants maintained as static const double with precise calculations

**Migration Notes:**
- Converted TypeScript enum to C typedef enum maintaining exact integer values
- Converted all static class properties/methods to C constants/functions with ClassName_ prefix
- Maintained exact physical constants: gravity (9.80665 m/s²), densities (1000/1020/1030 kg/m³)
- Preserved atmospheric calculation constants with precise floating-point values:
  - gasConstant = 8.31432 J/(mol·K)
  - temperature = 288.15 K (15°C)
  - lapsRate = -0.0065 K/m
  - molarMass = 0.0289644 kg/mol
- Calculated derived constants precisely:
  - exponent = -5.255876113278518
  - invertedExponent = -0.190263236508484
- Maintained exact barometric formula implementation from Wikipedia references
- All original comments preserved verbatim including Wikipedia reference URLs
- Used Math.pow() → pow() for power calculations with identical precision
- Maintained exact pressure comparison logic (>=) in altitude function

## Migration Quality Verification - Round 3

### Physics Module Precision Verification
- **Time Constants**: All time conversion constants maintain exact integer relationships (60, 3600, 86400, 180)
- **Physical Constants**: All physical constants preserved to full precision from TypeScript source
- **Atmospheric Calculations**: Barometric formula implementation identical to TypeScript with precise derived constants
- **Pressure Conversions**: Pascal/bar conversion factor maintained exactly (100000)
- **Water Density Values**: All salinity-based density values preserved exactly (1000/1020/1030 kg/m³)

### Code Coverage Verification - Physics Module
- ✅ All enums migrated (Salinity)
- ✅ All classes migrated (Time, Density, Gravity, PressureConverter, AltitudePressure)
- ✅ All static properties migrated to const double constants
- ✅ All static methods migrated to C functions
- ✅ All private static constants migrated to static const double
- ✅ All comments migrated verbatim including Wikipedia references
- ✅ All mathematical formulas preserved exactly

## Files Created - Round 3
9. `c_port/include/physics/time.h`
10. `c_port/src/physics/time.c`
11. `c_port/include/physics/pressureConverter.h`
12. `c_port/src/physics/pressureConverter.c`

## Next Steps
Ready for additional module migration and testing of completed components.