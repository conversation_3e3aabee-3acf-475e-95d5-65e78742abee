#include "common/precision.h"
#include <math.h>
#include <stdio.h>

// Internal helper functions (equivalent to private static methods)
static double Precision_adapt(double (*func)(double), double source, int digits);
static double Precision_adaptDistance(double (*func)(double), double source, double distance);

/** Unified helpers to deal with numbers precision in javascript */

/**
 * In the nitrox calculator, if we really need this fix, if round isn't enough
 * because of javascript numbers precision we need to help our self without rounding
 */
double Precision_fix(double source) {
    // Equivalent to Number(source.toFixed(10)) in JavaScript
    // Round to 10 decimal places to fix precision issues
    double precision = 1e10;
    return round(source * precision) / precision;
}

double Precision_roundTwoDecimals(double source) {
    return Precision_round_impl(source, 2);
}

// Implementation function for round with explicit digits parameter
double Precision_round_impl(double source, int digits) {
    return Precision_adapt(round, source, digits);
}

double Precision_floorTwoDecimals(double source) {
    return Precision_floor_impl(source, 2);
}

// Implementation function for floor with explicit digits parameter
double Precision_floor_impl(double source, int digits) {
    return Precision_adapt(floor, source, digits);
}

double Precision_ceilTwoDecimals(double source) {
    return Precision_ceil_impl(source, 2);
}

// Implementation function for ceil with explicit digits parameter
double Precision_ceil_impl(double source, int digits) {
    return Precision_adapt(ceil, source, digits);
}

double Precision_ceilDistance(double source, double distance) {
    return Precision_adaptDistance(ceil, source, distance);
}

double Precision_roundDistance(double source, double distance) {
    return Precision_adaptDistance(round, source, distance);
}

double Precision_floorDistance(double source, double distance) {
    return Precision_adaptDistance(floor, source, distance);
}

static double Precision_adapt(double (*func)(double), double source, int digits) {
    double precision = pow(10.0, (double)digits);
    return func(source * precision) / precision;
}

static double Precision_adaptDistance(double (*func)(double), double source, double distance) {
    return func(source / distance) * distance;
}