#include "common/binaryIntervalSearch.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* in our usage minimal step corresponds to one second. */
static const double minimalStep = 1.0;

double BinaryIntervalSearch_search(SearchContext* context) {
    if (context == NULL) {
        fprintf(stderr, "Error: SearchContext cannot be null\n");
        exit(EXIT_FAILURE);
    }
    
    if (context->maxValue < context->initialValue) {
        fprintf(stderr, "Error: Max value cant be smaller than initial value\n");
        exit(EXIT_FAILURE);
    }
    
    if (context->estimationStep > context->maxValue - context->initialValue) {
        fprintf(stderr, "Error: Step cant be larger than range\n");
        exit(EXIT_FAILURE);
    }
    
    Interval limits = BinaryIntervalSearch_findInitialLimit(context);
    double result = BinaryIntervalSearch_searchInsideInterval(context, &limits);
    return result;
}

double BinaryIntervalSearch_searchInsideInterval(SearchContext* context, Interval* limits) {
    while (limits->right - limits->left > minimalStep) {
        double middle = limits->left + (limits->right - limits->left) / 2.0;
        middle = round(middle);
        context->doWork(middle);
        
        if (context->meetsCondition()) {
            limits->left = middle;
        } else {
            limits->right = middle;
        }
    }
    
    return limits->left;
}

Interval BinaryIntervalSearch_findInitialLimit(SearchContext* context) {
    double current = context->initialValue;
    context->doWork(current);
    
    while (context->meetsCondition() && current <= context->maxValue) {
        current += context->estimationStep;
        context->doWork(current);
    }
    
    double leftLimit = current - context->estimationStep;
    leftLimit = leftLimit < context->initialValue ? context->initialValue : leftLimit;
    double rightLimit = current > context->maxValue ? context->maxValue : current;
    
    Interval result;
    result.left = leftLimit;
    result.right = rightLimit;
    
    return result;
}