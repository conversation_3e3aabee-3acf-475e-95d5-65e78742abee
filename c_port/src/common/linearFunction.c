#include "common/linearFunction.h"

/**
 * Calculate speed given x and y ranges
 */
double LinearFunction_speed(Range x, Range y) {
    double xChange = x.end - x.start;
    return LinearFunction_speedByXChange(y.start, y.end, xChange);
}

/**
 * Calculate speed given y start/end values and x change
 */
double LinearFunction_speedByXChange(double yStart, double yEnd, double xChange) {
    return (yEnd - yStart) / xChange;
}

/**
 * Calculate y value at a given x change
 */
double LinearFunction_yValueAt(double yStart, double speed, double xChange) {
    return yStart + speed * xChange;
}

/**
 * Returns absolute x value
 */
double LinearFunction_xValueAtAbsolute(Range x, Range y, double yValue) {
    double speed = LinearFunction_speed(x, y);
    return x.start + LinearFunction_xValueAt(y.start, speed, yValue);
}

/**
 * Returns relative X value, because the xStart is unknown
 */
double LinearFunction_xValueAt(double yStart, double speed, double yValue) {
    if (speed == 0.0) {
        return 0.0;
    }
    
    return (yValue - yStart) / speed;
}