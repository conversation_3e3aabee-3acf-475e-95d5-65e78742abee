#include "common/featureFlags.h"
#include <stddef.h>

/** Placeholder for features conditionally available. */
static FeatureFlags* _instance = NULL;
static FeatureFlags _instance_storage;

/**
 * Get the singleton instance of FeatureFlags
 * @return Pointer to the singleton FeatureFlags instance
 */
FeatureFlags* FeatureFlags_Instance(void) {
    if (_instance == NULL) {
        _instance = &_instance_storage;
        _instance->initialized = 1;
    }
    return _instance;
}