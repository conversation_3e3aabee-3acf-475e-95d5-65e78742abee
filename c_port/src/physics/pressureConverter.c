#include "physics/pressureConverter.h"

/**
 * Using constant, since still 3000 meters above the see the gravity is 9.79742 m/s2
 * https://en.wikipedia.org/wiki/Gravity_of_Earth#Mathematical_models
 */

/**
 * Standard gravity sample rates in meters per second per second (m/s2)
 */
const double Gravity_standard = 9.80665;

/**
 * 1000kg / m3 at 0C / 32F (standard conditions for measurements).
 */
const double Density_fresh = 1000.0;

/**
 * Brackish water density EN13319 - 1020kg / m3 at 0C / 32F (standard conditions for measurements).
 */
const double Density_brackish = 1020.0;

/**
 * 1030kg / m3 at 0C / 32F (standard conditions for measurements).
 */
const double Density_salt = 1030.0;

/**
 * https://en.wikipedia.org/wiki/Pressure#Units
 */

static const double PressureConverter_coefficient = 100000.0;

/**
 * Calculates the pascal to bar derived unit. 100000 pascals = 1 bar.
 *
 * @param pascals - The pascal SI derived unit.
 * @returns Bar derived unit of pressure from pascal.
 */
double PressureConverter_pascalToBar(double pascals) {
    return pascals / PressureConverter_coefficient;
}

/**
 * Calculates the bar to pascal derived unit. 100000 pascals = 1 bar.
 *
 * @param bars - The bar derived unit.
 * @returns Pascal derived unit of pressure from bars.
 */
double PressureConverter_barToPascal(double bars) {
    return bars * PressureConverter_coefficient;
}

/**
 * https://en.wikipedia.org/wiki/Barometric_formula#Derivation
 */

/** Standard pressure in bars */
const double AltitudePressure_standard = 1.01325;
static const double AltitudePressure_standardPascals = 101325.0; /* PressureConverter_barToPascal(AltitudePressure_standard) */

// https://en.wikipedia.org/wiki/Barometric_formula
// https://en.wikipedia.org/wiki/International_Standard_Atmosphere
static const double AltitudePressure_gasConstant = 8.31432; // J/(mol·K) for air
static const double AltitudePressure_temperature = 288.15; // kelvin = 15°C
static const double AltitudePressure_lapsRate = -0.0065;  // kelvin/meter
static const double AltitudePressure_molarMass = 0.0289644; // kg/mol
static const double AltitudePressure_exponent = -5.255876113278518; /* (Gravity_standard * AltitudePressure_molarMass) / (AltitudePressure_gasConstant * AltitudePressure_lapsRate) */
static const double AltitudePressure_invertedExponent = -0.19026323650848356; /* 1 / AltitudePressure_exponent */

/**
 * Calculates pressure at altitude in pascals
 *
 * @param altitude Positive number in meters representing the altitude
 */
double AltitudePressure_pressure(double altitude) {
    const double base = AltitudePressure_temperature / (AltitudePressure_temperature + AltitudePressure_lapsRate * altitude);
    return AltitudePressure_standardPascals * pow(base, AltitudePressure_exponent);
}

/**
 * Returns altitude in meters calculated from atmospheric pressure.
 * Returns 0, if pressure is lower than standard pressure
 * @param pressure in pascals
 */
double AltitudePressure_altitude(double pressure) {
    if(pressure >= AltitudePressure_standardPascals) {
        return 0.0;
    }

    const double pressureNormalized = pressure / AltitudePressure_standardPascals;
    const double base = pow(pressureNormalized, AltitudePressure_invertedExponent);
    return (AltitudePressure_temperature / base - AltitudePressure_temperature) / AltitudePressure_lapsRate;
}