#include "physics/time.h"

/** One seconds as base unit of decompression calculation. */
const double Time_oneSecond = 1.0;

/** One minute is 60 seconds */
const double Time_oneMinute = 60.0;

/** One hour is 3600 seconds */
const double Time_oneHour = 3600.0; /* Time_oneMinute * 60 */

/** Maximum deco stop duration one day (86400 seconds) */
const double Time_oneDay = 86400.0; /* Time_oneHour * 24 */

/** Default duration of the safety stop */
const double Time_safetyStopDuration = 180.0; /* Time_oneMinute * 3 */

/**
 * Converts duration in minutes to seconds
 *
 * @param minutes duration in minutes
 *
 * @returns amount seconds calculated from current duration
 */
double Time_toSeconds(double minutes) {
    return minutes * Time_oneMinute;
}

/**
 * Converts duration in seconds to minutes
 *
 * @param seconds duration in seconds
 *
 * @returns amount minutes calculated from seconds duration
 */
double Time_toMinutes(double seconds) {
    return seconds / Time_oneMinute;
}

/**
 * Converts duration in seconds to hours
 *
 * @param seconds duration in seconds
 *
 * @returns amount hours calculated from seconds duration
 */
double Time_toHours(double seconds) {
    return seconds / Time_oneHour;
}