#ifndef BINARY_INTERVAL_SEARCH_H
#define BINARY_INTERVAL_SEARCH_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Represents a time interval with left and right boundaries
 */
typedef struct {
    /** seconds */
    double left;
    
    /** seconds */
    double right;
} Interval;

/**
 * Context structure containing search parameters and callback functions
 */
typedef struct {
    /** Initial step used to find highest (upper boundary) limit */
    double estimationStep;
    /** Lower boundary for the search */
    double initialValue;
    /** Upper boundary extreme limit when searching its limit */
    double maxValue;
    /** method to be executed before calling meetsCondition */
    void (*doWork)(double newValue);
    /** Method used to decide, if the value is still in range */
    int (*meetsCondition)(void);
} SearchContext;

/**
 * High performance way to find next appropriate lowest higher value in sorted array.
 * Used to predict no decompression limit or deco stop duration based on ceiling.
 * Uses binary (half interval) search algorithm.
 * https://en.wikipedia.org/wiki/Binary_search_algorithm
 */

/**
 * Performs binary interval search using the provided context
 * @param context Search context containing parameters and callback functions
 * @return The search result value
 * @throws Error if maxValue < initialValue or estimationStep > range
 */
double BinaryIntervalSearch_search(SearchContext* context);

/**
 * Internal function to search within a specific interval
 * @param context Search context
 * @param limits The interval to search within
 * @return The search result value
 */
double BinaryIntervalSearch_searchInsideInterval(SearchContext* context, Interval* limits);

/**
 * Guess right upper value by adding step to current value and prevent left 0 or positive value
 * @param context Search context
 * @return Initial interval limits for the search
 */
Interval BinaryIntervalSearch_findInitialLimit(SearchContext* context);

#ifdef __cplusplus
}
#endif

#endif /* BINARY_INTERVAL_SEARCH_H */