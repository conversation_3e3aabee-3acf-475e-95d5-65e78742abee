#ifndef PRECISION_H
#define PRECISION_H

/** Unified helpers to deal with numbers precision in javascript */

/**
 * In the nitrox calculator, if we really need this fix, if round isn't enough
 * because of javascript numbers precision we need to help our self without rounding
 */
double Precision_fix(double source);

double Precision_roundTwoDecimals(double source);

// Implementation functions (renamed to _impl to support default parameters)
double Precision_round_impl(double source, int digits);
double Precision_floor_impl(double source, int digits);
double Precision_ceil_impl(double source, int digits);

// Helper macros for counting arguments
#define GET_ARG_COUNT(...) GET_ARG_COUNT_IMPL(__VA_ARGS__, 2, 1)
#define GET_ARG_COUNT_IMPL(_1, _2, N, ...) N

// Macro definitions to support default parameters (digits = 0)
#define Precision_round_1(source) Precision_round_impl(source, 0)
#define Precision_round_2(source, digits) Precision_round_impl(source, digits)
#define Precision_round(...) CONCAT(Precision_round_, GET_ARG_COUNT(__VA_ARGS__))(__VA_ARGS__)

#define Precision_floor_1(source) Precision_floor_impl(source, 0)
#define Precision_floor_2(source, digits) Precision_floor_impl(source, digits)
#define Precision_floor(...) CONCAT(Precision_floor_, GET_ARG_COUNT(__VA_ARGS__))(__VA_ARGS__)

#define Precision_ceil_1(source) Precision_ceil_impl(source, 0)
#define Precision_ceil_2(source, digits) Precision_ceil_impl(source, digits)
#define Precision_ceil(...) CONCAT(Precision_ceil_, GET_ARG_COUNT(__VA_ARGS__))(__VA_ARGS__)

// Helper macro for concatenation
#define CONCAT(a, b) CONCAT_IMPL(a, b)
#define CONCAT_IMPL(a, b) a##b

double Precision_floorTwoDecimals(double source);

double Precision_ceilTwoDecimals(double source);

double Precision_ceilDistance(double source, double distance);

double Precision_roundDistance(double source, double distance);

double Precision_floorDistance(double source, double distance);

#endif // PRECISION_H