#ifndef LINEAR_FUNCTION_H
#define LINEAR_FUNCTION_H

typedef struct {
    double start;
    double end;
} Range;

/**
 * Calculate speed given x and y ranges
 */
double LinearFunction_speed(Range x, Range y);

/**
 * Calculate speed given y start/end values and x change
 */
double LinearFunction_speedByXChange(double yStart, double yEnd, double xChange);

/**
 * Calculate y value at a given x change
 */
double LinearFunction_yValueAt(double yStart, double speed, double xChange);

/**
 * Returns absolute x value
 */
double LinearFunction_xValueAtAbsolute(Range x, Range y, double yValue);

/**
 * Returns relative X value, because the xStart is unknown
 */
double LinearFunction_xValueAt(double yStart, double speed, double yValue);

#endif // LINEAR_FUNCTION_H