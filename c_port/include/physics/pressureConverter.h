#ifndef PRESSURE_CONVERTER_H
#define PRESSURE_CONVERTER_H

#include <math.h>

/**
 * Pressure conversion utilities and atmospheric calculations.
 * Ported from projects/scuba-physics/src/lib/physics/pressure-converter.ts
 */

// Supported types of salt density of water used to distinguish depth converters
typedef enum {
    // 1000 kg/m3
    Salinity_fresh = 1,
    // EN13319 - 1020 kg/m3
    Salinity_brackish = 2,
    // 1030 kg/m3
    Salinity_salt = 3
} Salinity;

/**
 * Water density constants
 */

/**
 * 1000kg / m3 at 0C / 32F (standard conditions for measurements).
 */
extern const double Density_fresh;

/**
 * Brackish water density EN13319 - 1020kg / m3 at 0C / 32F (standard conditions for measurements).
 */
extern const double Density_brackish;

/**
 * 1030kg / m3 at 0C / 32F (standard conditions for measurements).
 */
extern const double Density_salt;

/**
 * Gravity constants
 */

/**
 * Standard gravity sample rates in meters per second per second (m/s2)
 */
extern const double Gravity_standard;

/**
 * Pressure conversion functions
 */

/**
 * Calculates the pascal to bar derived unit. 100000 pascals = 1 bar.
 *
 * @param pascals - The pascal SI derived unit.
 * @returns Bar derived unit of pressure from pascal.
 */
double PressureConverter_pascalToBar(double pascals);

/**
 * Calculates the bar to pascal derived unit. 100000 pascals = 1 bar.
 *
 * @param bars - The bar derived unit.
 * @returns Pascal derived unit of pressure from bars.
 */
double PressureConverter_barToPascal(double bars);

/**
 * Altitude pressure calculation functions
 */

/** Standard pressure in bars */
extern const double AltitudePressure_standard;

/**
 * Calculates pressure at altitude in pascals
 *
 * @param altitude Positive number in meters representing the altitude
 */
double AltitudePressure_pressure(double altitude);

/**
 * Returns altitude in meters calculated from atmospheric pressure.
 * Returns 0, if pressure is lower than standard pressure
 * @param pressure in pascals
 */
double AltitudePressure_altitude(double pressure);

#endif /* PRESSURE_CONVERTER_H */