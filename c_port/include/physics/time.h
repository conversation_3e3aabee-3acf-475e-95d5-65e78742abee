#ifndef TIME_H
#define TIME_H

/**
 * Time conversion utilities and constants for decompression calculations.
 * Ported from projects/scuba-physics/src/lib/physics/Time.ts
 */

/**
 * Time conversion constants and functions
 */

/** One seconds as base unit of decompression calculation. */
extern const double Time_oneSecond;

/** One minute is 60 seconds */
extern const double Time_oneMinute;

/** One hour is 3600 seconds */
extern const double Time_oneHour;

/** Maximum deco stop duration one day (86400 seconds) */
extern const double Time_oneDay;

/** Default duration of the safety stop */
extern const double Time_safetyStopDuration;

/**
 * Converts duration in minutes to seconds
 *
 * @param minutes duration in minutes
 *
 * @returns amount seconds calculated from current duration
 */
double Time_toSeconds(double minutes);

/**
 * Converts duration in seconds to minutes
 *
 * @param seconds duration in seconds
 *
 * @returns amount minutes calculated from seconds duration
 */
double Time_toMinutes(double seconds);

/**
 * Converts duration in seconds to hours
 *
 * @param seconds duration in seconds
 *
 * @returns amount hours calculated from seconds duration
 */
double Time_toHours(double seconds);

#endif /* TIME_H */