// Simple JavaScript test for PressureConverter module constants and functions

const Salinity = {
    fresh: 1,
    brackish: 2,
    salt: 3
};

class Density {
    static fresh = 1000;
    static brackish = 1020;
    static salt = 1030;
}

class Gravity {
    static standard = 9.80665;
}

class PressureConverter {
    static coefficient = 100000;

    static pascalToBar(pascals) {
        return pascals / PressureConverter.coefficient;
    }

    static barToPascal(bars) {
        return bars * PressureConverter.coefficient;
    }
}

class AltitudePressure {
    static standard = 1.01325;
    static standardPascals = PressureConverter.barToPascal(AltitudePressure.standard);

    // Constants from barometric formula
    static gasConstant = 8.31432; // J/(mol·K) for air
    static temperature = 288.15; // kelvin = 15°C
    static lapsRate = -0.0065;  // kelvin/meter
    static molarMass = 0.0289644; // kg/mol
    static exponent = (Gravity.standard * AltitudePressure.molarMass) /
        (AltitudePressure.gasConstant * AltitudePressure.lapsRate);
    static invertedExponent = 1 / AltitudePressure.exponent;

    static pressure(altitude) {
        const base = AltitudePressure.temperature / (AltitudePressure.temperature + AltitudePressure.lapsRate * altitude);
        return AltitudePressure.standardPascals * Math.pow(base, AltitudePressure.exponent);
    }

    static altitude(pressure) {
        if(pressure >= AltitudePressure.standardPascals) {
            return 0;
        }

        const pressureNormalized = pressure / AltitudePressure.standardPascals;
        const base = Math.pow(pressureNormalized, AltitudePressure.invertedExponent);
        return (AltitudePressure.temperature / base - AltitudePressure.temperature) / AltitudePressure.lapsRate;
    }
}

function runPressureConverterTests() {
    const result = {
        enumTests: {
            salinity: {
                fresh: Salinity.fresh,
                brackish: Salinity.brackish,
                salt: Salinity.salt
            }
        },
        constantTests: {
            density: {
                fresh: Density.fresh,
                brackish: Density.brackish,
                salt: Density.salt
            },
            gravity: {
                standard: Gravity.standard
            },
            altitudePressure: {
                standard: AltitudePressure.standard
            }
        },
        pressureConverterTests: {
            pascalToBar: [],
            barToPascal: []
        },
        altitudePressureTests: {
            pressure: [],
            altitude: []
        }
    };

    // Test cases for PressureConverter.pascalToBar function
    const pascalTestValues = [0, 100000, 200000, 50000, 101325, 300000, 1000000, 2500000];
    for (const pascals of pascalTestValues) {
        result.pressureConverterTests.pascalToBar.push({
            input: pascals,
            output: PressureConverter.pascalToBar(pascals)
        });
    }

    // Test cases for PressureConverter.barToPascal function
    const barTestValues = [0, 1, 2, 0.5, 1.01325, 3, 10, 25];
    for (const bars of barTestValues) {
        result.pressureConverterTests.barToPascal.push({
            input: bars,
            output: PressureConverter.barToPascal(bars)
        });
    }

    // Test cases for AltitudePressure.pressure function (altitude in meters)
    const altitudeTestValues = [0, 100, 500, 1000, 2000, 3000, 5000, 8000, 10000];
    for (const altitude of altitudeTestValues) {
        result.altitudePressureTests.pressure.push({
            input: altitude,
            output: AltitudePressure.pressure(altitude)
        });
    }

    // Test cases for AltitudePressure.altitude function (pressure in pascals)
    const pressureTestValues = [101325, 100000, 95000, 90000, 80000, 70000, 50000, 30000, 102000];
    for (const pressure of pressureTestValues) {
        result.altitudePressureTests.altitude.push({
            input: pressure,
            output: AltitudePressure.altitude(pressure)
        });
    }

    return result;
}

// Run tests and output results
const testResults = runPressureConverterTests();
console.log(JSON.stringify(testResults, null, 2));