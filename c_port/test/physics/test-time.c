#include <stdio.h>
#include <stdlib.h>
#include "../../include/physics/time.h"

/**
 * C implementation test for Time module
 * Tests all Time constants and conversion functions against TypeScript baseline
 */

void print_constant_tests() {
    printf("  \"constantTests\": {\n");
    printf("    \"oneSecond\": %.15g,\n", Time_oneSecond);
    printf("    \"oneMinute\": %.15g,\n", Time_oneMinute);
    printf("    \"oneHour\": %.15g,\n", Time_oneHour);
    printf("    \"oneDay\": %.15g,\n", Time_oneDay);
    printf("    \"safetyStopDuration\": %.15g\n", Time_safetyStopDuration);
    printf("  },\n");
}

void print_conversion_tests() {
    printf("  \"conversionTests\": {\n");
    
    // Test cases for toSeconds function
    printf("    \"toSeconds\": [\n");
    double minutesTestValues[] = {0, 1, 1.5, 5, 10, 60, 120.5, 1440};
    int minutesTestCount = sizeof(minutesTestValues) / sizeof(minutesTestValues[0]);
    
    for (int i = 0; i < minutesTestCount; i++) {
        double minutes = minutesTestValues[i];
        double result = Time_toSeconds(minutes);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", minutes);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < minutesTestCount - 1) ? "," : "");
    }
    printf("    ],\n");

    // Test cases for toMinutes function
    printf("    \"toMinutes\": [\n");
    double secondsTestValues[] = {0, 60, 90, 300, 600, 3600, 7230, 86400};
    int secondsTestCount = sizeof(secondsTestValues) / sizeof(secondsTestValues[0]);
    
    for (int i = 0; i < secondsTestCount; i++) {
        double seconds = secondsTestValues[i];
        double result = Time_toMinutes(seconds);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", seconds);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < secondsTestCount - 1) ? "," : "");
    }
    printf("    ],\n");

    // Test cases for toHours function
    printf("    \"toHours\": [\n");
    double secondsForHoursTestValues[] = {0, 3600, 5400, 18000, 36000, 86400, 172800, 259200};
    int secondsForHoursTestCount = sizeof(secondsForHoursTestValues) / sizeof(secondsForHoursTestValues[0]);
    
    for (int i = 0; i < secondsForHoursTestCount; i++) {
        double seconds = secondsForHoursTestValues[i];
        double result = Time_toHours(seconds);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", seconds);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < secondsForHoursTestCount - 1) ? "," : "");
    }
    printf("    ]\n");
    
    printf("  }\n");
}

int main() {
    printf("{\n");
    print_constant_tests();
    print_conversion_tests();
    printf("}\n");
    
    return 0;
}