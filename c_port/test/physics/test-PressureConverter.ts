import { Salinity, Density, Gravity, PressureConverter, AltitudePressure } from '../../../projects/scuba-physics/src/lib/physics/pressure-converter';

/**
 * TypeScript baseline test for PressureConverter module
 * Tests all constants, enums and conversion functions against C implementation
 */

interface PressureConverterTestResult {
    enumTests: {
        salinity: {
            fresh: number;
            brackish: number;
            salt: number;
        };
    };
    constantTests: {
        density: {
            fresh: number;
            brackish: number;
            salt: number;
        };
        gravity: {
            standard: number;
        };
        altitudePressure: {
            standard: number;
        };
    };
    pressureConverterTests: {
        pascalToBar: { input: number; output: number }[];
        barToPascal: { input: number; output: number }[];
    };
    altitudePressureTests: {
        pressure: { input: number; output: number }[];
        altitude: { input: number; output: number }[];
    };
}

function runPressureConverterTests(): PressureConverterTestResult {
    const result: PressureConverterTestResult = {
        enumTests: {
            salinity: {
                fresh: Salinity.fresh,
                brackish: Salinity.brackish,
                salt: Salinity.salt
            }
        },
        constantTests: {
            density: {
                fresh: Density.fresh,
                brackish: Density.brackish,
                salt: Density.salt
            },
            gravity: {
                standard: Gravity.standard
            },
            altitudePressure: {
                standard: AltitudePressure.standard
            }
        },
        pressureConverterTests: {
            pascalToBar: [],
            barToPascal: []
        },
        altitudePressureTests: {
            pressure: [],
            altitude: []
        }
    };

    // Test cases for PressureConverter.pascalToBar function
    const pascalTestValues = [0, 100000, 200000, 50000, 101325, 300000, 1000000, 2500000];
    for (const pascals of pascalTestValues) {
        result.pressureConverterTests.pascalToBar.push({
            input: pascals,
            output: PressureConverter.pascalToBar(pascals)
        });
    }

    // Test cases for PressureConverter.barToPascal function
    const barTestValues = [0, 1, 2, 0.5, 1.01325, 3, 10, 25];
    for (const bars of barTestValues) {
        result.pressureConverterTests.barToPascal.push({
            input: bars,
            output: PressureConverter.barToPascal(bars)
        });
    }

    // Test cases for AltitudePressure.pressure function (altitude in meters)
    const altitudeTestValues = [0, 100, 500, 1000, 2000, 3000, 5000, 8000, 10000];
    for (const altitude of altitudeTestValues) {
        result.altitudePressureTests.pressure.push({
            input: altitude,
            output: AltitudePressure.pressure(altitude)
        });
    }

    // Test cases for AltitudePressure.altitude function (pressure in pascals)
    const pressureTestValues = [101325, 100000, 95000, 90000, 80000, 70000, 50000, 30000, 102000];
    for (const pressure of pressureTestValues) {
        result.altitudePressureTests.altitude.push({
            input: pressure,
            output: AltitudePressure.altitude(pressure)
        });
    }

    return result;
}

// Run tests and output results
const testResults = runPressureConverterTests();
console.log(JSON.stringify(testResults, null, 2));