// Simple JavaScript test for Time module constants and functions

class Time {
    static oneSecond = 1;
    static oneMinute = 60;
    static oneHour = Time.oneMinute * 60;
    static oneDay = Time.oneHour * 24;
    static safetyStopDuration = Time.oneMinute * 3;

    static toSeconds(minutes) {
        return minutes * Time.oneMinute;
    }

    static toMinutes(seconds) {
        return seconds / Time.oneMinute;
    }

    static toHours(seconds) {
        return seconds / Time.oneHour;
    }
}

function runTimeTests() {
    const result = {
        constantTests: {
            oneSecond: Time.oneSecond,
            oneMinute: Time.oneMinute,
            oneHour: Time.oneHour,
            oneDay: Time.oneDay,
            safetyStopDuration: Time.safetyStopDuration
        },
        conversionTests: {
            toSeconds: [],
            toMinutes: [],
            toHours: []
        }
    };

    // Test cases for toSeconds function
    const minutesTestValues = [0, 1, 1.5, 5, 10, 60, 120.5, 1440];
    for (const minutes of minutesTestValues) {
        result.conversionTests.toSeconds.push({
            input: minutes,
            output: Time.toSeconds(minutes)
        });
    }

    // Test cases for toMinutes function  
    const secondsTestValues = [0, 60, 90, 300, 600, 3600, 7230, 86400];
    for (const seconds of secondsTestValues) {
        result.conversionTests.toMinutes.push({
            input: seconds,
            output: Time.toMinutes(seconds)
        });
    }

    // Test cases for toHours function
    const secondsForHoursTestValues = [0, 3600, 5400, 18000, 36000, 86400, 172800, 259200];
    for (const seconds of secondsForHoursTestValues) {
        result.conversionTests.toHours.push({
            input: seconds,
            output: Time.toHours(seconds)
        });
    }

    return result;
}

// Run tests and output results
const testResults = runTimeTests();
console.log(JSON.stringify(testResults, null, 2));