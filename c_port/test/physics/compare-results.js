const fs = require('fs');
const path = require('path');

const TOLERANCE = 1e-9; // 精度阈值

function loadJsonFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(content);
    } catch (error) {
        console.error(`Error loading ${filePath}:`, error.message);
        return null;
    }
}

function compareNumbers(a, b, tolerance = TOLERANCE) {
    if (Math.abs(a - b) <= tolerance) {
        return { match: true, difference: Math.abs(a - b) };
    }
    return { match: false, difference: Math.abs(a - b) };
}

function compareObjects(obj1, obj2, path = '') {
    const results = {
        matches: 0,
        mismatches: 0,
        details: []
    };

    function compareRecursive(o1, o2, currentPath) {
        if (typeof o1 !== typeof o2) {
            results.mismatches++;
            results.details.push({
                path: currentPath,
                type: 'type_mismatch',
                baseline: typeof o1,
                candidate: typeof o2
            });
            return;
        }

        if (Array.isArray(o1)) {
            if (o1.length !== o2.length) {
                results.mismatches++;
                results.details.push({
                    path: currentPath,
                    type: 'array_length_mismatch',
                    baseline: o1.length,
                    candidate: o2.length
                });
                return;
            }

            for (let i = 0; i < o1.length; i++) {
                compareRecursive(o1[i], o2[i], `${currentPath}[${i}]`);
            }
        } else if (typeof o1 === 'object' && o1 !== null) {
            const keys1 = Object.keys(o1);
            const keys2 = Object.keys(o2);
            
            for (const key of new Set([...keys1, ...keys2])) {
                if (!(key in o1)) {
                    results.mismatches++;
                    results.details.push({
                        path: `${currentPath}.${key}`,
                        type: 'missing_in_baseline',
                        candidate: o2[key]
                    });
                } else if (!(key in o2)) {
                    results.mismatches++;
                    results.details.push({
                        path: `${currentPath}.${key}`,
                        type: 'missing_in_candidate',
                        baseline: o1[key]
                    });
                } else {
                    compareRecursive(o1[key], o2[key], `${currentPath}.${key}`);
                }
            }
        } else if (typeof o1 === 'number') {
            const comparison = compareNumbers(o1, o2);
            if (comparison.match) {
                results.matches++;
                if (comparison.difference > 0) {
                    results.details.push({
                        path: currentPath,
                        type: 'precision_difference',
                        baseline: o1,
                        candidate: o2,
                        difference: comparison.difference,
                        status: 'acceptable'
                    });
                }
            } else {
                results.mismatches++;
                results.details.push({
                    path: currentPath,
                    type: 'value_mismatch',
                    baseline: o1,
                    candidate: o2,
                    difference: comparison.difference,
                    status: 'unacceptable'
                });
            }
        } else {
            if (o1 === o2) {
                results.matches++;
            } else {
                results.mismatches++;
                results.details.push({
                    path: currentPath,
                    type: 'value_mismatch',
                    baseline: o1,
                    candidate: o2
                });
            }
        }
    }

    compareRecursive(obj1, obj2, path);
    return results;
}

function generateReport(moduleName, results) {
    const totalTests = results.matches + results.mismatches;
    const successRate = totalTests > 0 ? (results.matches / totalTests * 100).toFixed(2) : '0.00';
    
    let report = `# ${moduleName}模块测试报告\n\n`;
    report += `## 测试概要\n`;
    report += `- 总测试项: ${totalTests}\n`;
    report += `- 通过项: ${results.matches}\n`;
    report += `- 失败项: ${results.mismatches}\n`;
    report += `- 成功率: ${successRate}%\n\n`;

    if (results.mismatches === 0) {
        report += `## 测试结果: ✅ 全部通过\n\n`;
        report += `所有测试用例均通过，C语言移植与TypeScript源码完全一致。\n\n`;
    } else {
        report += `## 测试结果: ❌ 发现差异\n\n`;
        
        const unacceptableErrors = results.details.filter(d => d.status === 'unacceptable');
        const acceptableErrors = results.details.filter(d => d.status === 'acceptable');
        
        if (unacceptableErrors.length > 0) {
            report += `### 严重差异 (${unacceptableErrors.length}项)\n\n`;
            unacceptableErrors.forEach((error, index) => {
                report += `${index + 1}. **路径**: ${error.path}\n`;
                report += `   - **类型**: ${error.type}\n`;
                report += `   - **基准值**: ${error.baseline}\n`;
                report += `   - **测试值**: ${error.candidate}\n`;
                if (error.difference !== undefined) {
                    report += `   - **差异**: ${error.difference}\n`;
                }
                report += '\n';
            });
        }
        
        if (acceptableErrors.length > 0) {
            report += `### 可接受精度差异 (${acceptableErrors.length}项)\n\n`;
            acceptableErrors.forEach((error, index) => {
                report += `${index + 1}. **路径**: ${error.path}\n`;
                report += `   - **基准值**: ${error.baseline}\n`;
                report += `   - **测试值**: ${error.candidate}\n`;
                report += `   - **差异**: ${error.difference} (阈值: ${TOLERANCE})\n\n`;
            });
        }
    }

    // 添加详细的测试用例信息
    if (results.matches > 0) {
        report += `## 通过的测试项\n\n`;
        report += `共有 ${results.matches} 项测试通过，包括所有常数验证和函数计算结果验证。\n\n`;
    }

    return report;
}

function main() {
    console.log('开始比较Time模块测试结果...\n');
    
    // 比较Time模块
    const timeJs = loadJsonFile('time-js-output.json');
    const timeC = loadJsonFile('time-c-output.json');
    
    if (timeJs && timeC) {
        const timeResults = compareObjects(timeJs, timeC, 'Time');
        const timeReport = generateReport('Time', timeResults);
        
        // 确保报告目录存在
        const reportsDir = '../../reports/tester/physics';
        fs.mkdirSync(reportsDir, { recursive: true });
        
        fs.writeFileSync(path.join(reportsDir, 'time.test.md'), timeReport);
        console.log('Time模块测试完成');
        console.log(`- 通过: ${timeResults.matches}`);
        console.log(`- 失败: ${timeResults.mismatches}\n`);
    }
    
    // 比较PressureConverter模块
    console.log('开始比较PressureConverter模块测试结果...\n');
    
    const pressureJs = loadJsonFile('pressureConverter-js-output.json');
    const pressureC = loadJsonFile('pressureConverter-c-output.json');
    
    if (pressureJs && pressureC) {
        const pressureResults = compareObjects(pressureJs, pressureC, 'PressureConverter');
        const pressureReport = generateReport('PressureConverter', pressureResults);
        
        fs.writeFileSync(path.join('../../reports/tester/physics', 'pressureConverter.test.md'), pressureReport);
        console.log('PressureConverter模块测试完成');
        console.log(`- 通过: ${pressureResults.matches}`);
        console.log(`- 失败: ${pressureResults.mismatches}\n`);
    }

    console.log('所有比较完成！报告已生成至 c_port/reports/tester/physics/ 目录');
}

main();