#include <stdio.h>
#include <stdlib.h>
#include "../../include/physics/pressureConverter.h"

/**
 * C implementation test for PressureConverter module
 * Tests all constants, enums and conversion functions against TypeScript baseline
 */

void print_enum_tests() {
    printf("  \"enumTests\": {\n");
    printf("    \"salinity\": {\n");
    printf("      \"fresh\": %d,\n", Salinity_fresh);
    printf("      \"brackish\": %d,\n", Salinity_brackish);
    printf("      \"salt\": %d\n", Salinity_salt);
    printf("    }\n");
    printf("  },\n");
}

void print_constant_tests() {
    printf("  \"constantTests\": {\n");
    printf("    \"density\": {\n");
    printf("      \"fresh\": %.15g,\n", Density_fresh);
    printf("      \"brackish\": %.15g,\n", Density_brackish);
    printf("      \"salt\": %.15g\n", Density_salt);
    printf("    },\n");
    printf("    \"gravity\": {\n");
    printf("      \"standard\": %.15g\n", Gravity_standard);
    printf("    },\n");
    printf("    \"altitudePressure\": {\n");
    printf("      \"standard\": %.15g\n", AltitudePressure_standard);
    printf("    }\n");
    printf("  },\n");
}

void print_pressure_converter_tests() {
    printf("  \"pressureConverterTests\": {\n");
    
    // Test cases for PressureConverter_pascalToBar function
    printf("    \"pascalToBar\": [\n");
    double pascalTestValues[] = {0, 100000, 200000, 50000, 101325, 300000, 1000000, 2500000};
    int pascalTestCount = sizeof(pascalTestValues) / sizeof(pascalTestValues[0]);
    
    for (int i = 0; i < pascalTestCount; i++) {
        double pascals = pascalTestValues[i];
        double result = PressureConverter_pascalToBar(pascals);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", pascals);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < pascalTestCount - 1) ? "," : "");
    }
    printf("    ],\n");

    // Test cases for PressureConverter_barToPascal function
    printf("    \"barToPascal\": [\n");
    double barTestValues[] = {0, 1, 2, 0.5, 1.01325, 3, 10, 25};
    int barTestCount = sizeof(barTestValues) / sizeof(barTestValues[0]);
    
    for (int i = 0; i < barTestCount; i++) {
        double bars = barTestValues[i];
        double result = PressureConverter_barToPascal(bars);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", bars);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < barTestCount - 1) ? "," : "");
    }
    printf("    ]\n");
    
    printf("  },\n");
}

void print_altitude_pressure_tests() {
    printf("  \"altitudePressureTests\": {\n");
    
    // Test cases for AltitudePressure_pressure function (altitude in meters)
    printf("    \"pressure\": [\n");
    double altitudeTestValues[] = {0, 100, 500, 1000, 2000, 3000, 5000, 8000, 10000};
    int altitudeTestCount = sizeof(altitudeTestValues) / sizeof(altitudeTestValues[0]);
    
    for (int i = 0; i < altitudeTestCount; i++) {
        double altitude = altitudeTestValues[i];
        double result = AltitudePressure_pressure(altitude);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", altitude);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < altitudeTestCount - 1) ? "," : "");
    }
    printf("    ],\n");

    // Test cases for AltitudePressure_altitude function (pressure in pascals)
    printf("    \"altitude\": [\n");
    double pressureTestValues[] = {101325, 100000, 95000, 90000, 80000, 70000, 50000, 30000, 102000};
    int pressureTestCount = sizeof(pressureTestValues) / sizeof(pressureTestValues[0]);
    
    for (int i = 0; i < pressureTestCount; i++) {
        double pressure = pressureTestValues[i];
        double result = AltitudePressure_altitude(pressure);
        printf("      {\n");
        printf("        \"input\": %.15g,\n", pressure);
        printf("        \"output\": %.15g\n", result);
        printf("      }%s\n", (i < pressureTestCount - 1) ? "," : "");
    }
    printf("    ]\n");
    
    printf("  }\n");
}

int main() {
    printf("{\n");
    print_enum_tests();
    print_constant_tests();
    print_pressure_converter_tests();
    print_altitude_pressure_tests();
    printf("}\n");
    
    return 0;
}