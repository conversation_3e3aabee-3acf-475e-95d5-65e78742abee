# Physics模块测试使用说明

## 测试文件结构

```
c_port/test/physics/
├── test-Time.ts              # TypeScript Time模块测试(原始版本)
├── test-Time.js              # JavaScript Time模块测试(实际使用)
├── test-time.c               # C Time模块测试
├── test-PressureConverter.ts # TypeScript PressureConverter测试(原始版本)  
├── test-PressureConverter.js # JavaScript PressureConverter测试(实际使用)
├── test-pressureConverter.c  # C PressureConverter测试
├── compare-results.js        # 结果比较脚本
└── README-test-usage.md      # 本使用说明文件
```

## 如何运行测试

### 1. 编译C测试程序

```bash
# 编译Time模块测试
gcc -I./include -lm -o test/physics/test-time test/physics/test-time.c src/physics/time.c

# 编译PressureConverter模块测试  
gcc -I./include -o test/physics/test-pressureConverter test/physics/test-pressureConverter.c src/physics/pressureConverter.c -lm
```

### 2. 运行基准测试(JavaScript)

```bash
# 运行Time模块JavaScript测试
node test/physics/test-Time.js > test/physics/time-js-output.json

# 运行PressureConverter模块JavaScript测试
node test/physics/test-PressureConverter.js > test/physics/pressureConverter-js-output.json
```

### 3. 运行候选测试(C)

```bash  
# 运行Time模块C测试
./test/physics/test-time > test/physics/time-c-output.json

# 运行PressureConverter模块C测试
./test/physics/test-pressureConverter > test/physics/pressureConverter-c-output.json
```

### 4. 比较结果并生成报告

```bash
# 创建报告目录
mkdir -p reports/tester/physics

# 运行比较脚本
cd test/physics && node compare-results.js
```

## 测试覆盖范围

### Time模块测试
- **常数验证**: oneSecond, oneMinute, oneHour, oneDay, safetyStopDuration
- **转换函数**: toSeconds(), toMinutes(), toHours()
- **测试用例**: 包含零值、整数、小数、大数值等多种边界条件

### PressureConverter模块测试  
- **枚举验证**: Salinity枚举的所有值
- **物理常数**: 密度、重力常数、标准大气压力
- **简单转换**: pascalToBar(), barToPascal()
- **复杂计算**: 大气压力公式 AltitudePressure_pressure(), AltitudePressure_altitude()
- **测试用例**: 涵盖典型物理量值和边界条件

## 精度要求

- **数值精度阈值**: 1e-9
- **比较标准**: 绝对差值小于等于阈值视为匹配
- **特殊处理**: 整数值要求完全相等，浮点数允许精度误差

## 报告文件

测试完成后会在以下位置生成报告：

```
c_port/reports/tester/physics/
├── time.test.md                    # Time模块测试报告
├── pressureConverter.test.md       # PressureConverter模块测试报告
└── physics-modules-summary.test.md # 综合测试报告
```

## 故障排除

### 编译错误
- 确保包含路径正确: `-I./include`
- 链接数学库: `-lm` (对于使用pow()等数学函数的代码)

### 运行时错误
- 检查可执行文件权限
- 确保输入文件路径正确
- 验证JSON输出格式是否正确

### 精度差异
- 检查C语言中的常数定义精度
- 验证数学运算的精度损失
- 调整精度阈值(如需要)

## 扩展测试

如需添加新的测试用例：

1. 在相应的JS和C测试文件中添加相同的测试数据
2. 确保测试数据覆盖新的边界条件
3. 重新运行完整的测试流程
4. 检查比较报告中的结果

## 注意事项

- TypeScript原始测试文件(.ts)仅作为参考，实际使用JavaScript版本
- C测试程序需要手动编译，注意链接必要的库
- 所有测试使用相同的测试数据确保公平比较
- 报告自动生成，包含详细的差异分析