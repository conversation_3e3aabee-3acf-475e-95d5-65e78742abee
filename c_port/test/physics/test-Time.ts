const { Time } = require('../../../projects/scuba-physics/src/lib/physics/Time');

/**
 * TypeScript baseline test for Time module
 * Tests all Time constants and conversion functions against C implementation
 */

interface TimeTestResult {
    constantTests: {
        oneSecond: number;
        oneMinute: number;
        oneHour: number;
        oneDay: number;
        safetyStopDuration: number;
    };
    conversionTests: {
        toSeconds: { input: number; output: number }[];
        toMinutes: { input: number; output: number }[];
        toHours: { input: number; output: number }[];
    };
}

function runTimeTests(): TimeTestResult {
    const result: TimeTestResult = {
        constantTests: {
            oneSecond: Time.oneSecond,
            oneMinute: Time.oneMinute,
            oneHour: Time.oneHour,
            oneDay: Time.oneDay,
            safetyStopDuration: Time.safetyStopDuration
        },
        conversionTests: {
            toSeconds: [],
            toMinutes: [],
            toHours: []
        }
    };

    // Test cases for toSeconds function
    const minutesTestValues = [0, 1, 1.5, 5, 10, 60, 120.5, 1440];
    for (const minutes of minutesTestValues) {
        result.conversionTests.toSeconds.push({
            input: minutes,
            output: Time.toSeconds(minutes)
        });
    }

    // Test cases for toMinutes function  
    const secondsTestValues = [0, 60, 90, 300, 600, 3600, 7230, 86400];
    for (const seconds of secondsTestValues) {
        result.conversionTests.toMinutes.push({
            input: seconds,
            output: Time.toMinutes(seconds)
        });
    }

    // Test cases for toHours function
    const secondsForHoursTestValues = [0, 3600, 5400, 18000, 36000, 86400, 172800, 259200];
    for (const seconds of secondsForHoursTestValues) {
        result.conversionTests.toHours.push({
            input: seconds,
            output: Time.toHours(seconds)
        });
    }

    return result;
}

// Run tests and output results
const testResults = runTimeTests();
console.log(JSON.stringify(testResults, null, 2));