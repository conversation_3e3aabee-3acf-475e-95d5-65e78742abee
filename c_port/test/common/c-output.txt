=== BinaryIntervalSearch C Tests ===

Running basic search tests...
Running boundary tests...
Running edge case tests...
Running error condition tests...

=== Test Results ===

Test: basic_search_100
  Result: 100.000000000
  DoWork calls: 16
  MeetsCondition calls: 16
  DoWork values: [count: 16]

Test: basic_search_250
  Result: 250.000000000
  DoWork calls: 17
  MeetsCondition calls: 17
  DoWork values: [count: 17]

Test: small_range_search
  Result: 18.000000000
  DoWork calls: 7
  MeetsCondition calls: 7
  DoWork values: [count: 7]

Test: target_at_initial
  Result: 100.000000000
  DoWork calls: 1
  MeetsCondition calls: 1
  DoWork values: [count: 1]

Test: target_at_max
  Result: 100.000000000
  DoWork calls: 12
  MeetsCondition calls: 12
  DoWork values: [count: 12]

Test: small_step
  Result: 25.000000000
  DoWork calls: 27
  MeetsCondition calls: 27
  DoWork values: [count: 27]

Test: initial_equals_max
  Error: Step cant be larger than range
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: [count: 0]

Test: large_numbers
  Result: 5500.000000000
  DoWork calls: 54
  MeetsCondition calls: 54
  DoWork values: [count: 54]

Test: fractional_step
  Result: 12.000000000
  DoWork calls: 8
  MeetsCondition calls: 8
  DoWork values: [count: 8]

Test: max_less_than_initial
  Error: Max value cant be smaller than initial value
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: [count: 0]

Test: step_larger_than_range
  Error: Step cant be larger than range
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: [count: 0]

=== Structured Results for Comparison ===
{
  "results": [
    {
      "testName": "basic_search_100",
      "result": 100.000000000,
      "doWorkCallCount": 16,
      "meetsConditionCallCount": 16,
      "doWorkValuesCount": 16,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "basic_search_250",
      "result": 250.000000000,
      "doWorkCallCount": 17,
      "meetsConditionCallCount": 17,
      "doWorkValuesCount": 17,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "small_range_search",
      "result": 18.000000000,
      "doWorkCallCount": 7,
      "meetsConditionCallCount": 7,
      "doWorkValuesCount": 7,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "target_at_initial",
      "result": 100.000000000,
      "doWorkCallCount": 1,
      "meetsConditionCallCount": 1,
      "doWorkValuesCount": 1,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "target_at_max",
      "result": 100.000000000,
      "doWorkCallCount": 12,
      "meetsConditionCallCount": 12,
      "doWorkValuesCount": 12,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "small_step",
      "result": 25.000000000,
      "doWorkCallCount": 27,
      "meetsConditionCallCount": 27,
      "doWorkValuesCount": 27,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "initial_equals_max",
      "result": -1.000000000,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Step cant be larger than range"
    },
    {
      "testName": "large_numbers",
      "result": 5500.000000000,
      "doWorkCallCount": 54,
      "meetsConditionCallCount": 54,
      "doWorkValuesCount": 54,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "fractional_step",
      "result": 12.000000000,
      "doWorkCallCount": 8,
      "meetsConditionCallCount": 8,
      "doWorkValuesCount": 8,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "max_less_than_initial",
      "result": -1.000000000,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Max value cant be smaller than initial value"
    },
    {
      "testName": "step_larger_than_range",
      "result": -1.000000000,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Step cant be larger than range"
    }
  ]
}
