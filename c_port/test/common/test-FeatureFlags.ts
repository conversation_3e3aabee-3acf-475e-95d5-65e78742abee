import { FeatureFlags } from '../../../projects/scuba-physics/src/lib/common/featureFlags';

/**
 * TypeScript test file for FeatureFlags module
 * Tests the original TypeScript implementation 
 * This test file should produce identical results to test-featureFlags.c
 */

// Test results storage
interface TestResult {
    testName: string;
    result: any;
    success: boolean;
    error?: string;
}

const testResults: TestResult[] = [];

function runTest(testName: string, testFunc: () => any): void {
    try {
        const result = testFunc();
        testResults.push({
            testName,
            result,
            success: true
        });
        console.log(`[TS] ${testName}: ${JSON.stringify(result)}`);
    } catch (error) {
        testResults.push({
            testName,
            result: null,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
        console.log(`[TS] ${testName}: ERROR - ${error}`);
    }
}

function main(): void {
    console.log('=== FeatureFlags TypeScript Tests ===');
    
    // Test 1: Singleton Instance Creation
    runTest('Singleton_Instance_Creation', () => {
        const instance1 = FeatureFlags.Instance;
        return {
            instance_exists: instance1 !== null && instance1 !== undefined,
            instance_type: typeof instance1
        };
    });
    
    // Test 2: Singleton Instance Consistency  
    runTest('Singleton_Instance_Consistency', () => {
        const instance1 = FeatureFlags.Instance;
        const instance2 = FeatureFlags.Instance;
        return {
            same_instance: instance1 === instance2,
            both_exist: instance1 !== null && instance2 !== null
        };
    });
    
    // Test 3: Instance Properties
    runTest('Instance_Properties', () => {
        const instance = FeatureFlags.Instance;
        return {
            is_object: typeof instance === 'object',
            constructor_name: instance.constructor.name,
            instance_of_feature_flags: instance instanceof FeatureFlags
        };
    });
    
    // Test 4: Multiple Access Pattern
    runTest('Multiple_Access_Pattern', () => {
        const results = [];
        for (let i = 0; i < 5; i++) {
            const instance = FeatureFlags.Instance;
            results.push({
                attempt: i + 1,
                exists: instance !== null,
                type: typeof instance
            });
        }
        
        // Check if all instances are the same reference
        const firstInstance = FeatureFlags.Instance;
        const allSame = results.every(() => FeatureFlags.Instance === firstInstance);
        
        return {
            access_attempts: results,
            all_same_reference: allSame
        };
    });
    
    // Test 5: Constructor Access (should not be possible)
    runTest('Constructor_Access_Restriction', () => {
        try {
            // This should throw an error as constructor is private
            // @ts-ignore - Intentionally accessing private constructor for test
            new FeatureFlags();
            return { can_construct: true, error: null };
        } catch (error) {
            return { 
                can_construct: false, 
                error_message: error instanceof Error ? error.message : String(error)
            };
        }
    });
    
    // Summary
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    console.log('\n=== FeatureFlags TypeScript Test Summary ===');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    
    if (passedTests < totalTests) {
        console.log('\nFailed Tests:');
        testResults.filter(r => !r.success).forEach(result => {
            console.log(`- ${result.testName}: ${result.error}`);
        });
    }
}

// Run tests
main();

// Export results for comparison
export { testResults };