// Simple Node.js runner for TypeScript tests
const path = require('path');
const fs = require('fs');

// Mock TypeScript modules for testing
function createFeatureFlagsMock() {
    return {
        FeatureFlags: class FeatureFlags {
            static _instance;
            
            constructor() {
                if (FeatureFlags._instance) {
                    throw new Error('Cannot construct singleton instance');
                }
            }
            
            static get Instance() {
                return FeatureFlags._instance || (FeatureFlags._instance = new FeatureFlags());
            }
        }
    };
}

function createPrecisionMock() {
    return {
        Precision: class Precision {
            static fix(source) {
                const result = Number(source.toFixed(10));
                return result;
            }
            
            static roundTwoDecimals(source) {
                return this.round(source, 2);
            }
            
            static round(source, digits = 0) {
                return this.adapt(Math.round, source, digits);
            }
            
            static floorTwoDecimals(source) {
                return this.floor(source, 2);
            }
            
            static floor(source, digits = 0) {
                return this.adapt(Math.floor, source, digits);
            }
            
            static ceilTwoDecimals(source) {
                return this.ceil(source, 2);
            }
            
            static ceil(source, digits = 0) {
                return this.adapt(Math.ceil, source, digits);
            }
            
            static ceilDistance(source, distance) {
                return this.adaptDistance(Math.ceil, source, distance);
            }
            
            static roundDistance(source, distance) {
                return this.adaptDistance(Math.round, source, distance);
            }
            
            static floorDistance(source, distance) {
                return this.adaptDistance(Math.floor, source, distance);
            }
            
            static adapt(func, source, digits) {
                const precision = Math.pow(10, digits);
                return func(source * precision) / precision;
            }
            
            static adaptDistance(func, source, distance) {
                return func(source / distance) * distance;
            }
        }
    };
}

function createLinearFunctionMock() {
    return {
        LinearFunction: class LinearFunction {
            static speed(x, y) {
                const xChange = x.end - x.start;
                return LinearFunction.speedByXChange(y.start, y.end, xChange);
            }
            
            static speedByXChange(yStart, yEnd, xChange) {
                return (yEnd - yStart) / xChange;
            }
            
            static yValueAt(yStart, speed, xChange) {
                return yStart + speed * xChange;
            }
            
            static xValueAtAbsolute(x, y, yValue) {
                const speed = this.speed(x, y);
                return x.start + LinearFunction.xValueAt(y.start, speed, yValue);
            }
            
            static xValueAt(yStart, speed, yValue) {
                if(speed === 0) {
                    return 0;
                }
                return (yValue - yStart) / speed;
            }
        },
        Range: class Range {
            constructor(start, end) {
                this.start = start;
                this.end = end;
            }
        }
    };
}

// Run tests for each module
function runFeatureFlagsTest() {
    console.log('=== Running FeatureFlags TypeScript Test ===');
    const { FeatureFlags } = createFeatureFlagsMock();
    
    // Test results storage
    const testResults = [];
    
    function runTest(testName, testFunc) {
        try {
            const result = testFunc();
            testResults.push({
                testName,
                result,
                success: true
            });
            console.log(`[TS] ${testName}: ${JSON.stringify(result)}`);
        } catch (error) {
            testResults.push({
                testName,
                result: null,
                success: false,
                error: error.message
            });
            console.log(`[TS] ${testName}: ERROR - ${error.message}`);
        }
    }
    
    // Test 1: Singleton Instance Creation
    runTest('Singleton_Instance_Creation', () => {
        const instance1 = FeatureFlags.Instance;
        return {
            instance_exists: instance1 !== null && instance1 !== undefined,
            instance_type: typeof instance1
        };
    });
    
    // Test 2: Singleton Instance Consistency  
    runTest('Singleton_Instance_Consistency', () => {
        const instance1 = FeatureFlags.Instance;
        const instance2 = FeatureFlags.Instance;
        return {
            same_instance: instance1 === instance2,
            both_exist: instance1 !== null && instance2 !== null
        };
    });
    
    // Test 3: Instance Properties
    runTest('Instance_Properties', () => {
        const instance = FeatureFlags.Instance;
        return {
            is_object: typeof instance === 'object',
            constructor_name: instance.constructor.name,
            instance_of_feature_flags: instance instanceof FeatureFlags
        };
    });
    
    // Test 4: Multiple Access Pattern
    runTest('Multiple_Access_Pattern', () => {
        const results = [];
        for (let i = 0; i < 5; i++) {
            const instance = FeatureFlags.Instance;
            results.push({
                attempt: i + 1,
                exists: instance !== null,
                type: typeof instance
            });
        }
        
        // Check if all instances are the same reference
        const firstInstance = FeatureFlags.Instance;
        const allSame = results.every(() => FeatureFlags.Instance === firstInstance);
        
        return {
            access_attempts: results,
            all_same_reference: allSame
        };
    });
    
    // Test 5: Constructor Access (should not be possible)
    runTest('Constructor_Access_Restriction', () => {
        try {
            new FeatureFlags();
            return { can_construct: true, error: null };
        } catch (error) {
            return { 
                can_construct: false, 
                error_message: error.message
            };
        }
    });
    
    // Summary
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    console.log('\n=== FeatureFlags TypeScript Test Summary ===');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    
    return testResults;
}

function runPrecisionTest() {
    console.log('\n=== Running Precision TypeScript Test ===');
    const { Precision } = createPrecisionMock();
    
    // Test results storage
    const testResults = [];
    
    function runTest(testName, testFunc) {
        try {
            const result = testFunc();
            testResults.push({
                testName,
                result,
                success: true
            });
            console.log(`[TS] ${testName}: ${JSON.stringify(result)}`);
        } catch (error) {
            testResults.push({
                testName,
                result: null,
                success: false,
                error: error.message
            });
            console.log(`[TS] ${testName}: ERROR - ${error.message}`);
        }
    }
    
    // Test 1: Precision.fix function
    runTest('Precision_Fix_Function', () => {
        const testCases = [
            1.23456789123456789,
            0.1 + 0.2,  // Classic JavaScript precision issue
            3.141592653589793238,
            -2.718281828459045,
            0.0000000001,
            1000000000.123456789,
            0.0
        ];
        
        return testCases.map(value => ({
            input: value,
            output: Precision.fix(value)
        }));
    });
    
    // Test 2: Round functions with different digits
    runTest('Round_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            round_0: Precision.round(testValue, 0),
            round_1: Precision.round(testValue, 1),
            round_2: Precision.round(testValue, 2),
            round_3: Precision.round(testValue, 3),
            round_default: Precision.round(testValue), // Should default to 0
            roundTwoDecimals: Precision.roundTwoDecimals(testValue)
        };
    });
    
    // Test 3: Floor functions with different digits
    runTest('Floor_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            floor_0: Precision.floor(testValue, 0),
            floor_1: Precision.floor(testValue, 1),
            floor_2: Precision.floor(testValue, 2),
            floor_3: Precision.floor(testValue, 3),
            floor_default: Precision.floor(testValue), // Should default to 0
            floorTwoDecimals: Precision.floorTwoDecimals(testValue)
        };
    });
    
    // Test 4: Ceil functions with different digits
    runTest('Ceil_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            ceil_0: Precision.ceil(testValue, 0),
            ceil_1: Precision.ceil(testValue, 1),
            ceil_2: Precision.ceil(testValue, 2),
            ceil_3: Precision.ceil(testValue, 3),
            ceil_default: Precision.ceil(testValue), // Should default to 0
            ceilTwoDecimals: Precision.ceilTwoDecimals(testValue)
        };
    });
    
    // Add more tests here...
    
    // Summary
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    console.log('\n=== Precision TypeScript Test Summary ===');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    
    return testResults;
}

// Run all tests
console.log('Starting TypeScript Tests...');
const featureFlagsResults = runFeatureFlagsTest();
const precisionResults = runPrecisionTest();

console.log('\n=== All TypeScript Tests Complete ===');