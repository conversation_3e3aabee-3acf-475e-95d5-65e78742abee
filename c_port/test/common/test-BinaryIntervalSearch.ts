// Import types and class directly since we can't use module imports in this context
interface SearchContext {
    /** Initial step used to find highest (upper boundary) limit */
    estimationStep: number;
    /** Lower boundary for the search */
    initialValue: number;
    /** Upper boundary extreme limit when searching its limit */
    maxValue: number;
    /** method to be executed before calling meetsCondition */
    doWork: (newValue: number) => void;
    /** Method used to decide, if the value is still in range */
    meetsCondition: () => boolean;
}

interface Interval {
    /** seconds */
    left: number;
    /** seconds */
    right: number;
}

/**
 * High performance way to find next appropriate lowest higher value in sorted array.
 * Used to predict no decompression limit or deco stop duration based on ceiling.
 * Uses binary (half interval) search algorithm.
 */
class BinaryIntervalSearch {
    /* in our usage minimal step corresponds to one second. */
    private static readonly minimalStep = 1;
    constructor() { }

    public search(context: SearchContext): number {
        if(context.maxValue < context.initialValue){
            throw Error('Max value cant be smaller than initial value');
        }

        if(context.estimationStep > context.maxValue - context.initialValue){
            throw Error('Step cant be larger than range');
        }

        const limits = this.findInitialLimit(context);
        const result = this.searchInsideInterval(context, limits);
        return result;
    }

    private searchInsideInterval(context: SearchContext, limits: Interval): number {
        while (limits.right - limits.left > BinaryIntervalSearch.minimalStep) {
            let middle = limits.left + (limits.right - limits.left) / 2;
            middle = Math.round(middle);
            context.doWork(middle);

            if (context.meetsCondition()) {
                limits.left = middle;
            } else {
                limits.right = middle;
            }
        }

        return limits.left;
    }

    /** Guess right upper value by adding step to current value and prevent left 0 or positive value */
    private findInitialLimit(context: SearchContext): Interval {
        let current = context.initialValue;
        context.doWork(current);

        while (context.meetsCondition() && current <= context.maxValue) {
            current += context.estimationStep;
            context.doWork(current);
        }

        let leftLimit = current - context.estimationStep;
        leftLimit = leftLimit < context.initialValue ? context.initialValue : leftLimit;
        const rightLimit = current > context.maxValue ? context.maxValue : current;

        return {
            left: leftLimit,
            right: rightLimit
        };
    }
}

interface TestResult {
    testName: string;
    result: number;
    doWorkCallCount: number;
    meetsConditionCallCount: number;
    doWorkValues: number[];
    error?: string;
}

interface TestCase {
    name: string;
    context: {
        estimationStep: number;
        initialValue: number;
        maxValue: number;
    };
    targetValue: number; // The target value that meetsCondition will check against
    expectedApproximate?: number; // Expected result for validation
}

class TestRunner {
    private results: TestResult[] = [];

    run(): void {
        console.log('=== BinaryIntervalSearch TypeScript Tests ===\n');
        
        this.runBasicSearchTests();
        this.runBoundaryTests();
        this.runEdgeCaseTests();
        this.runErrorConditionTests();
        
        this.printResults();
    }

    private runBasicSearchTests(): void {
        console.log('Running basic search tests...');

        // Test case 1: Simple search scenario
        this.runTest({
            name: 'basic_search_100',
            context: {
                estimationStep: 10,
                initialValue: 0,
                maxValue: 1000
            },
            targetValue: 100
        });

        // Test case 2: Different target value
        this.runTest({
            name: 'basic_search_250',
            context: {
                estimationStep: 20,
                initialValue: 50,
                maxValue: 500
            },
            targetValue: 250
        });

        // Test case 3: Small range search
        this.runTest({
            name: 'small_range_search',
            context: {
                estimationStep: 2,
                initialValue: 10,
                maxValue: 30
            },
            targetValue: 18
        });
    }

    private runBoundaryTests(): void {
        console.log('Running boundary tests...');

        // Test case 4: Target at initial value
        this.runTest({
            name: 'target_at_initial',
            context: {
                estimationStep: 5,
                initialValue: 100,
                maxValue: 200
            },
            targetValue: 99 // Less than initial, so should return initial
        });

        // Test case 5: Target at max value
        this.runTest({
            name: 'target_at_max',
            context: {
                estimationStep: 10,
                initialValue: 0,
                maxValue: 100
            },
            targetValue: 150 // Greater than max, should find closest
        });

        // Test case 6: Very small step
        this.runTest({
            name: 'small_step',
            context: {
                estimationStep: 1,
                initialValue: 0,
                maxValue: 50
            },
            targetValue: 25
        });
    }

    private runEdgeCaseTests(): void {
        console.log('Running edge case tests...');

        // Test case 7: Initial = Max
        this.runTest({
            name: 'initial_equals_max',
            context: {
                estimationStep: 1,
                initialValue: 100,
                maxValue: 100
            },
            targetValue: 50 // Less than initial/max
        });

        // Test case 8: Large numbers
        this.runTest({
            name: 'large_numbers',
            context: {
                estimationStep: 100,
                initialValue: 1000,
                maxValue: 10000
            },
            targetValue: 5500
        });

        // Test case 9: Fractional values in step
        this.runTest({
            name: 'fractional_step',
            context: {
                estimationStep: 2.5,
                initialValue: 0,
                maxValue: 50
            },
            targetValue: 12.3
        });
    }

    private runErrorConditionTests(): void {
        console.log('Running error condition tests...');

        // Test case 10: Max < Initial (should throw error)
        this.runErrorTest({
            name: 'max_less_than_initial',
            context: {
                estimationStep: 10,
                initialValue: 100,
                maxValue: 50
            },
            targetValue: 75
        });

        // Test case 11: Step larger than range (should throw error)
        this.runErrorTest({
            name: 'step_larger_than_range',
            context: {
                estimationStep: 200,
                initialValue: 10,
                maxValue: 50
            },
            targetValue: 30
        });
    }

    private runTest(testCase: TestCase): void {
        let doWorkCallCount = 0;
        let meetsConditionCallCount = 0;
        const doWorkValues: number[] = [];
        let currentTestValue = 0;

        const context: SearchContext = {
            estimationStep: testCase.context.estimationStep,
            initialValue: testCase.context.initialValue,
            maxValue: testCase.context.maxValue,
            doWork: (newValue: number) => {
                doWorkCallCount++;
                doWorkValues.push(newValue);
                currentTestValue = newValue;
            },
            meetsCondition: () => {
                meetsConditionCallCount++;
                return currentTestValue <= testCase.targetValue;
            }
        };

        try {
            const searcher = new BinaryIntervalSearch();
            const result = searcher.search(context);

            this.results.push({
                testName: testCase.name,
                result: result,
                doWorkCallCount: doWorkCallCount,
                meetsConditionCallCount: meetsConditionCallCount,
                doWorkValues: doWorkValues
            });

        } catch (error) {
            this.results.push({
                testName: testCase.name,
                result: -1,
                doWorkCallCount: doWorkCallCount,
                meetsConditionCallCount: meetsConditionCallCount,
                doWorkValues: doWorkValues,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }

    private runErrorTest(testCase: TestCase): void {
        this.runTest(testCase); // Error handling is the same, just expecting an error
    }

    private printResults(): void {
        console.log('\n=== Test Results ===');
        
        for (const result of this.results) {
            console.log(`\nTest: ${result.testName}`);
            if (result.error) {
                console.log(`  Error: ${result.error}`);
            } else {
                console.log(`  Result: ${result.result}`);
            }
            console.log(`  DoWork calls: ${result.doWorkCallCount}`);
            console.log(`  MeetsCondition calls: ${result.meetsConditionCallCount}`);
            console.log(`  DoWork values: [${result.doWorkValues.join(', ')}]`);
        }

        // Output structured data for comparison
        console.log('\n=== Structured Results for Comparison ===');
        const structuredResults = {
            results: this.results.map(r => ({
                testName: r.testName,
                result: r.result,
                doWorkCallCount: r.doWorkCallCount,
                meetsConditionCallCount: r.meetsConditionCallCount,
                doWorkValuesCount: r.doWorkValues.length,
                hasError: !!r.error,
                errorMessage: r.error || null
            }))
        };
        console.log(JSON.stringify(structuredResults, null, 2));
    }
}

// Run the tests
const testRunner = new TestRunner();
testRunner.run();