import { LinearFunction, Range } from '../../../projects/scuba-physics/src/lib/common/linearFunction';

/**
 * TypeScript test file for LinearFunction module
 * Tests the original TypeScript implementation 
 * This test file should produce identical results to test-linearFunction.c
 */

// Test results storage
interface TestResult {
    testName: string;
    result: any;
    success: boolean;
    error?: string;
}

const testResults: TestResult[] = [];

function runTest(testName: string, testFunc: () => any): void {
    try {
        const result = testFunc();
        testResults.push({
            testName,
            result,
            success: true
        });
        console.log(`[TS] ${testName}: ${JSON.stringify(result)}`);
    } catch (error) {
        testResults.push({
            testName,
            result: null,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
        console.log(`[TS] ${testName}: ERROR - ${error}`);
    }
}

function main(): void {
    console.log('=== LinearFunction TypeScript Tests ===');
    
    // Test 1: Speed calculation from ranges
    runTest('Speed_From_Ranges', () => {
        const testCases = [
            {
                x: { start: 0, end: 10 },
                y: { start: 0, end: 20 }
            },
            {
                x: { start: 5, end: 15 },
                y: { start: 100, end: 200 }
            },
            {
                x: { start: -5, end: 5 },
                y: { start: -10, end: 10 }
            },
            {
                x: { start: 0, end: 1 },
                y: { start: 0, end: 0 }  // Zero slope
            },
            {
                x: { start: 10, end: 10 },
                y: { start: 5, end: 15 }  // Zero x change (should result in Infinity)
            }
        ];
        
        return testCases.map((testCase, index) => ({
            case: index + 1,
            x: testCase.x,
            y: testCase.y,
            speed: LinearFunction.speed(testCase.x, testCase.y)
        }));
    });
    
    // Test 2: Speed by X change
    runTest('Speed_By_X_Change', () => {
        const testCases = [
            { yStart: 0, yEnd: 10, xChange: 5 },
            { yStart: 100, yEnd: 200, xChange: 10 },
            { yStart: -50, yEnd: 50, xChange: 20 },
            { yStart: 10, yEnd: 10, xChange: 5 },  // No y change
            { yStart: 0, yEnd: 10, xChange: 0 }   // Zero x change (should result in Infinity)
        ];
        
        return testCases.map((testCase, index) => ({
            case: index + 1,
            yStart: testCase.yStart,
            yEnd: testCase.yEnd,
            xChange: testCase.xChange,
            speed: LinearFunction.speedByXChange(testCase.yStart, testCase.yEnd, testCase.xChange)
        }));
    });
    
    // Test 3: Y value at X change
    runTest('Y_Value_At_X_Change', () => {
        const testCases = [
            { yStart: 0, speed: 2, xChange: 5 },
            { yStart: 100, speed: -3, xChange: 10 },
            { yStart: 50, speed: 0, xChange: 20 },  // Zero speed
            { yStart: -10, speed: 1.5, xChange: -4 },
            { yStart: 0, speed: 0.5, xChange: 0 }  // Zero x change
        ];
        
        return testCases.map((testCase, index) => ({
            case: index + 1,
            yStart: testCase.yStart,
            speed: testCase.speed,
            xChange: testCase.xChange,
            yValue: LinearFunction.yValueAt(testCase.yStart, testCase.speed, testCase.xChange)
        }));
    });
    
    // Test 4: X value at absolute (complete ranges)
    runTest('X_Value_At_Absolute', () => {
        const testCases = [
            {
                x: { start: 0, end: 10 },
                y: { start: 0, end: 20 },
                yValue: 10  // Should be at x = 5
            },
            {
                x: { start: 5, end: 15 },
                y: { start: 100, end: 200 },
                yValue: 150  // Should be at x = 10
            },
            {
                x: { start: 0, end: 1 },
                y: { start: 10, end: 10 },
                yValue: 10  // Horizontal line
            },
            {
                x: { start: -5, end: 5 },
                y: { start: -10, end: 10 },
                yValue: 0  // Should be at x = 0
            }
        ];
        
        return testCases.map((testCase, index) => ({
            case: index + 1,
            x: testCase.x,
            y: testCase.y,
            yValue: testCase.yValue,
            xAbsolute: LinearFunction.xValueAtAbsolute(testCase.x, testCase.y, testCase.yValue)
        }));
    });
    
    // Test 5: X value at (relative)
    runTest('X_Value_At_Relative', () => {
        const testCases = [
            { yStart: 0, speed: 2, yValue: 10 },  // Should be x = 5
            { yStart: 100, speed: -5, yValue: 50 },  // Should be x = 10
            { yStart: 50, speed: 0, yValue: 50 },  // Zero speed, same y value
            { yStart: 50, speed: 0, yValue: 60 },  // Zero speed, different y value (should be 0)
            { yStart: -10, speed: 0.5, yValue: 0 },  // Should be x = 20
            { yStart: 0, speed: 1, yValue: 0 }  // Should be x = 0
        ];
        
        return testCases.map((testCase, index) => ({
            case: index + 1,
            yStart: testCase.yStart,
            speed: testCase.speed,
            yValue: testCase.yValue,
            xRelative: LinearFunction.xValueAt(testCase.yStart, testCase.speed, testCase.yValue)
        }));
    });
    
    // Test 6: Zero speed edge cases
    runTest('Zero_Speed_Edge_Cases', () => {
        return [
            {
                description: "Zero speed, same y value",
                yStart: 10,
                speed: 0,
                yValue: 10,
                xValue: LinearFunction.xValueAt(10, 0, 10)
            },
            {
                description: "Zero speed, different y value",
                yStart: 10,
                speed: 0,
                yValue: 20,
                xValue: LinearFunction.xValueAt(10, 0, 20)
            },
            {
                description: "Zero speed from horizontal range",
                x: { start: 0, end: 10 },
                y: { start: 5, end: 5 },
                yValue: 5,
                xAbsolute: LinearFunction.xValueAtAbsolute({ start: 0, end: 10 }, { start: 5, end: 5 }, 5)
            }
        ];
    });
    
    // Test 7: Negative values handling
    runTest('Negative_Values_Handling', () => {
        return [
            {
                description: "Negative ranges",
                x: { start: -10, end: -5 },
                y: { start: -20, end: -10 },
                speed: LinearFunction.speed({ start: -10, end: -5 }, { start: -20, end: -10 }),
                yValue: LinearFunction.yValueAt(-20, LinearFunction.speed({ start: -10, end: -5 }, { start: -20, end: -10 }), 3)
            },
            {
                description: "Mixed positive/negative",
                x: { start: -5, end: 5 },
                y: { start: -10, end: 10 },
                speed: LinearFunction.speed({ start: -5, end: 5 }, { start: -10, end: 10 }),
                xAbsolute: LinearFunction.xValueAtAbsolute({ start: -5, end: 5 }, { start: -10, end: 10 }, 0)
            }
        ];
    });
    
    // Test 8: Large number precision
    runTest('Large_Number_Precision', () => {
        const largeX = { start: 1000000, end: 2000000 };
        const largeY = { start: 5000000, end: 6000000 };
        const speed = LinearFunction.speed(largeX, largeY);
        
        return {
            x: largeX,
            y: largeY,
            speed: speed,
            yAt500000: LinearFunction.yValueAt(largeY.start, speed, 500000),
            xAtMidpoint: LinearFunction.xValueAtAbsolute(largeX, largeY, 5500000)
        };
    });
    
    // Test 9: Small number precision
    runTest('Small_Number_Precision', () => {
        const smallX = { start: 0.0001, end: 0.0002 };
        const smallY = { start: 0.0005, end: 0.0006 };
        const speed = LinearFunction.speed(smallX, smallY);
        
        return {
            x: smallX,
            y: smallY,
            speed: speed,
            yAt0_00005: LinearFunction.yValueAt(smallY.start, speed, 0.00005),
            xAtMidpoint: LinearFunction.xValueAtAbsolute(smallX, smallY, 0.00055)
        };
    });
    
    // Test 10: Consistency between methods
    runTest('Method_Consistency', () => {
        const x: Range = { start: 10, end: 20 };
        const y: Range = { start: 50, end: 100 };
        const speed = LinearFunction.speed(x, y);
        const speedByChange = LinearFunction.speedByXChange(y.start, y.end, x.end - x.start);
        
        // Test that both speed methods give the same result
        const speedsMatch = Math.abs(speed - speedByChange) < 1e-10;
        
        // Test consistency between absolute and relative x calculations
        const testYValue = 75;
        const xAbsolute = LinearFunction.xValueAtAbsolute(x, y, testYValue);
        const xRelative = LinearFunction.xValueAt(y.start, speed, testYValue);
        const xCalculated = x.start + xRelative;
        const xValuesMatch = Math.abs(xAbsolute - xCalculated) < 1e-10;
        
        return {
            x: x,
            y: y,
            speed: speed,
            speedByChange: speedByChange,
            speedsMatch: speedsMatch,
            testYValue: testYValue,
            xAbsolute: xAbsolute,
            xRelative: xRelative,
            xCalculated: xCalculated,
            xValuesMatch: xValuesMatch
        };
    });
    
    // Summary
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    console.log('\n=== LinearFunction TypeScript Test Summary ===');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    
    if (passedTests < totalTests) {
        console.log('\nFailed Tests:');
        testResults.filter(r => !r.success).forEach(result => {
            console.log(`- ${result.testName}: ${result.error}`);
        });
    }
}

// Run tests
main();

// Export results for comparison
export { testResults };