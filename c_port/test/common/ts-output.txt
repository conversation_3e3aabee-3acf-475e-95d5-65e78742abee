=== BinaryIntervalSearch TypeScript Tests ===

Running basic search tests...
Running boundary tests...
Running edge case tests...
Running error condition tests...

=== Test Results ===

Test: basic_search_100
  Result: 100
  DoWork calls: 16
  MeetsCondition calls: 16
  DoWork values: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 105, 103, 102, 101]

Test: basic_search_250
  Result: 250
  DoWork calls: 17
  MeetsCondition calls: 17
  DoWork values: [50, 70, 90, 110, 130, 150, 170, 190, 210, 230, 250, 270, 260, 255, 253, 252, 251]

Test: small_range_search
  Result: 18
  DoWork calls: 7
  MeetsCondition calls: 7
  DoWork values: [10, 12, 14, 16, 18, 20, 19]

Test: target_at_initial
  Result: 100
  DoWork calls: 1
  MeetsCondition calls: 1
  DoWork values: [100]

Test: target_at_max
  Result: 100
  DoWork calls: 12
  MeetsCondition calls: 12
  DoWork values: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110]

Test: small_step
  Result: 25
  DoWork calls: 27
  MeetsCondition calls: 27
  DoWork values: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]

Test: initial_equals_max
  Error: Step cant be larger than range
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: []

Test: large_numbers
  Result: 5500
  DoWork calls: 54
  MeetsCondition calls: 54
  DoWork values: [1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100, 2200, 2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000, 3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000, 4100, 4200, 4300, 4400, 4500, 4600, 4700, 4800, 4900, 5000, 5100, 5200, 5300, 5400, 5500, 5600, 5550, 5525, 5513, 5507, 5504, 5502, 5501]

Test: fractional_step
  Result: 12
  DoWork calls: 8
  MeetsCondition calls: 8
  DoWork values: [0, 2.5, 5, 7.5, 10, 12.5, 11, 12]

Test: max_less_than_initial
  Error: Max value cant be smaller than initial value
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: []

Test: step_larger_than_range
  Error: Step cant be larger than range
  DoWork calls: 0
  MeetsCondition calls: 0
  DoWork values: []

=== Structured Results for Comparison ===
{
  "results": [
    {
      "testName": "basic_search_100",
      "result": 100,
      "doWorkCallCount": 16,
      "meetsConditionCallCount": 16,
      "doWorkValuesCount": 16,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "basic_search_250",
      "result": 250,
      "doWorkCallCount": 17,
      "meetsConditionCallCount": 17,
      "doWorkValuesCount": 17,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "small_range_search",
      "result": 18,
      "doWorkCallCount": 7,
      "meetsConditionCallCount": 7,
      "doWorkValuesCount": 7,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "target_at_initial",
      "result": 100,
      "doWorkCallCount": 1,
      "meetsConditionCallCount": 1,
      "doWorkValuesCount": 1,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "target_at_max",
      "result": 100,
      "doWorkCallCount": 12,
      "meetsConditionCallCount": 12,
      "doWorkValuesCount": 12,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "small_step",
      "result": 25,
      "doWorkCallCount": 27,
      "meetsConditionCallCount": 27,
      "doWorkValuesCount": 27,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "initial_equals_max",
      "result": -1,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Step cant be larger than range"
    },
    {
      "testName": "large_numbers",
      "result": 5500,
      "doWorkCallCount": 54,
      "meetsConditionCallCount": 54,
      "doWorkValuesCount": 54,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "fractional_step",
      "result": 12,
      "doWorkCallCount": 8,
      "meetsConditionCallCount": 8,
      "doWorkValuesCount": 8,
      "hasError": false,
      "errorMessage": null
    },
    {
      "testName": "max_less_than_initial",
      "result": -1,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Max value cant be smaller than initial value"
    },
    {
      "testName": "step_larger_than_range",
      "result": -1,
      "doWorkCallCount": 0,
      "meetsConditionCallCount": 0,
      "doWorkValuesCount": 0,
      "hasError": true,
      "errorMessage": "Step cant be larger than range"
    }
  ]
}
