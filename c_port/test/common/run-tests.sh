#!/bin/bash

echo "=== BinaryIntervalSearch Comparative Tests ==="
echo

# Change to project root for TypeScript compilation
echo "Running TypeScript tests..."
cd /home/<USER>/work/port1/GasPlanner_TS2C
npx ts-node c_port/test/common/test-BinaryIntervalSearch.ts > c_port/test/common/ts-output.txt 2>&1

# Change back to test directory
cd c_port/test/common

# Compile and run C tests
echo "Compiling and running C tests..."
gcc -Wall -Wextra -std=c99 -I../../include -o test-binaryIntervalSearch test-binaryIntervalSearch.c ../../src/common/binaryIntervalSearch.c -lm
./test-binaryIntervalSearch > c-output.txt 2>&1

# Compare results
echo "Comparing results..."
python3 compare-results.py

echo
echo "Test completed. Report available at:"
echo "  c_port/reports/tester/common/BinaryIntervalSearch.test.md"