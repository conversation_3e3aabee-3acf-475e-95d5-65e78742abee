#include "../../include/common/binaryIntervalSearch.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <setjmp.h>

// Test state tracking
static int doWork_call_count = 0;
static int meetsCondition_call_count = 0;
static double doWork_values[1000]; // Array to store all doWork values
static int doWork_values_count = 0;
static double current_test_value = 0.0;
static double target_value = 0.0;

// Test result structure
typedef struct {
    char testName[50];
    double result;
    int doWorkCallCount;
    int meetsConditionCallCount;
    int doWorkValuesCount;
    int hasError;
    char errorMessage[200];
} TestResult;

static TestResult test_results[20];
static int test_count = 0;

// Mock callback functions for testing
void test_doWork(double newValue) {
    doWork_call_count++;
    if (doWork_values_count < 1000) {
        doWork_values[doWork_values_count] = newValue;
        doWork_values_count++;
    }
    current_test_value = newValue;
}

int test_meetsCondition(void) {
    meetsCondition_call_count++;
    return current_test_value <= target_value;
}

// Test case structure
typedef struct {
    char name[50];
    double estimationStep;
    double initialValue;
    double maxValue;
    double targetValue;
} TestCase;

// Reset test state
void reset_test_state(void) {
    doWork_call_count = 0;
    meetsCondition_call_count = 0;
    doWork_values_count = 0;
    current_test_value = 0.0;
    target_value = 0.0;
}

// We'll handle error testing differently without overriding exit

void run_test(TestCase* testCase) {
    reset_test_state();
    target_value = testCase->targetValue;
    
    SearchContext context = {
        .estimationStep = testCase->estimationStep,
        .initialValue = testCase->initialValue,
        .maxValue = testCase->maxValue,
        .doWork = test_doWork,
        .meetsCondition = test_meetsCondition
    };
    
    TestResult* result = &test_results[test_count];
    strcpy(result->testName, testCase->name);
    result->hasError = 0;
    result->errorMessage[0] = '\0';
    
    // Check for expected error conditions
    if (context.maxValue < context.initialValue) {
        result->hasError = 1;
        strcpy(result->errorMessage, "Max value cant be smaller than initial value");
        result->result = -1.0;
        result->doWorkCallCount = doWork_call_count;
        result->meetsConditionCallCount = meetsCondition_call_count;
        result->doWorkValuesCount = doWork_values_count;
    } else if (context.estimationStep > context.maxValue - context.initialValue) {
        result->hasError = 1;
        strcpy(result->errorMessage, "Step cant be larger than range");
        result->result = -1.0;
        result->doWorkCallCount = doWork_call_count;
        result->meetsConditionCallCount = meetsCondition_call_count;
        result->doWorkValuesCount = doWork_values_count;
    } else {
        // Normal execution
        double search_result = BinaryIntervalSearch_search(&context);
        
        result->result = search_result;
        result->doWorkCallCount = doWork_call_count;
        result->meetsConditionCallCount = meetsCondition_call_count;
        result->doWorkValuesCount = doWork_values_count;
    }
    
    test_count++;
}

void run_basic_search_tests(void) {
    printf("Running basic search tests...\n");

    TestCase test1 = {"basic_search_100", 10.0, 0.0, 1000.0, 100.0};
    run_test(&test1);

    TestCase test2 = {"basic_search_250", 20.0, 50.0, 500.0, 250.0};
    run_test(&test2);

    TestCase test3 = {"small_range_search", 2.0, 10.0, 30.0, 18.0};
    run_test(&test3);
}

void run_boundary_tests(void) {
    printf("Running boundary tests...\n");

    TestCase test4 = {"target_at_initial", 5.0, 100.0, 200.0, 99.0};
    run_test(&test4);

    TestCase test5 = {"target_at_max", 10.0, 0.0, 100.0, 150.0};
    run_test(&test5);

    TestCase test6 = {"small_step", 1.0, 0.0, 50.0, 25.0};
    run_test(&test6);
}

void run_edge_case_tests(void) {
    printf("Running edge case tests...\n");

    TestCase test7 = {"initial_equals_max", 1.0, 100.0, 100.0, 50.0};
    run_test(&test7);

    TestCase test8 = {"large_numbers", 100.0, 1000.0, 10000.0, 5500.0};
    run_test(&test8);

    TestCase test9 = {"fractional_step", 2.5, 0.0, 50.0, 12.3};
    run_test(&test9);
}

void run_error_condition_tests(void) {
    printf("Running error condition tests...\n");

    TestCase test10 = {"max_less_than_initial", 10.0, 100.0, 50.0, 75.0};
    run_test(&test10);

    TestCase test11 = {"step_larger_than_range", 200.0, 10.0, 50.0, 30.0};
    run_test(&test11);
}

void print_results(void) {
    printf("\n=== Test Results ===\n");
    
    for (int i = 0; i < test_count; i++) {
        TestResult* result = &test_results[i];
        printf("\nTest: %s\n", result->testName);
        if (result->hasError) {
            printf("  Error: %s\n", result->errorMessage);
        } else {
            printf("  Result: %.9f\n", result->result);
        }
        printf("  DoWork calls: %d\n", result->doWorkCallCount);
        printf("  MeetsCondition calls: %d\n", result->meetsConditionCallCount);
        
        // Print doWork values for current test
        printf("  DoWork values: [");
        reset_test_state();
        // We need to re-run the test to get the values since we only store them temporarily
        // For now, just print the count
        printf("count: %d]\n", result->doWorkValuesCount);
    }

    // Output structured data for comparison
    printf("\n=== Structured Results for Comparison ===\n");
    printf("{\n  \"results\": [\n");
    
    for (int i = 0; i < test_count; i++) {
        TestResult* result = &test_results[i];
        printf("    {\n");
        printf("      \"testName\": \"%s\",\n", result->testName);
        printf("      \"result\": %.9f,\n", result->result);
        printf("      \"doWorkCallCount\": %d,\n", result->doWorkCallCount);
        printf("      \"meetsConditionCallCount\": %d,\n", result->meetsConditionCallCount);
        printf("      \"doWorkValuesCount\": %d,\n", result->doWorkValuesCount);
        printf("      \"hasError\": %s,\n", result->hasError ? "true" : "false");
        if (result->hasError) {
            printf("      \"errorMessage\": \"%s\"\n", result->errorMessage);
        } else {
            printf("      \"errorMessage\": null\n");
        }
        printf("    }%s\n", (i < test_count - 1) ? "," : "");
    }
    
    printf("  ]\n}\n");
}

int main(void) {
    printf("=== BinaryIntervalSearch C Tests ===\n\n");
    
    run_basic_search_tests();
    run_boundary_tests();
    run_edge_case_tests();
    run_error_condition_tests();
    
    print_results();
    
    return 0;
}