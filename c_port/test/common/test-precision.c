#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "../../include/common/precision.h"

/**
 * C test file for precision module
 * Tests the C port implementation 
 * This test file should produce identical results to test-Precision.ts
 */

// Test results structure
typedef struct {
    char testName[100];
    char result[2000];  // Larger buffer for complex results
    int success;
    char error[200];
} TestResult;

TestResult testResults[15];
int testCount = 0;

void runTest(const char* testName, void (*testFunc)(TestResult*)) {
    TestResult* result = &testResults[testCount];
    strcpy(result->testName, testName);
    result->success = 1;
    result->error[0] = '\0';
    
    printf("[C] %s: ", testName);
    testFunc(result);
    
    if (result->success) {
        printf("%s\n", result->result);
    } else {
        printf("ERROR - %s\n", result->error);
    }
    
    testCount++;
}

void test_precision_fix_function(TestResult* result) {
    double testCases[] = {
        1.23456789123456789,
        0.1 + 0.2,  // Classic precision issue
        3.141592653589793238,
        -2.718281828459045,
        0.0000000001,
        1000000000.123456789,
        0.0
    };
    int numCases = sizeof(testCases) / sizeof(testCases[0]);
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double input = testCases[i];
        double output = Precision_fix(input);
        char caseStr[150];
        snprintf(caseStr, sizeof(caseStr),
            "{\"input\": %.15g, \"output\": %.15g}%s",
            input, output, (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_round_functions_various_digits(TestResult* result) {
    double testValue = 123.456789;
    
    snprintf(result->result, sizeof(result->result),
        "{\"round_0\": %.15g, \"round_1\": %.15g, \"round_2\": %.15g, "
        "\"round_3\": %.15g, \"round_default\": %.15g, \"roundTwoDecimals\": %.15g}",
        Precision_round(testValue, 0),
        Precision_round(testValue, 1), 
        Precision_round(testValue, 2),
        Precision_round(testValue, 3),
        Precision_round(testValue),  // Default parameter (should be 0)
        Precision_roundTwoDecimals(testValue));
}

void test_floor_functions_various_digits(TestResult* result) {
    double testValue = 123.456789;
    
    snprintf(result->result, sizeof(result->result),
        "{\"floor_0\": %.15g, \"floor_1\": %.15g, \"floor_2\": %.15g, "
        "\"floor_3\": %.15g, \"floor_default\": %.15g, \"floorTwoDecimals\": %.15g}",
        Precision_floor(testValue, 0),
        Precision_floor(testValue, 1),
        Precision_floor(testValue, 2), 
        Precision_floor(testValue, 3),
        Precision_floor(testValue),  // Default parameter (should be 0)
        Precision_floorTwoDecimals(testValue));
}

void test_ceil_functions_various_digits(TestResult* result) {
    double testValue = 123.456789;
    
    snprintf(result->result, sizeof(result->result),
        "{\"ceil_0\": %.15g, \"ceil_1\": %.15g, \"ceil_2\": %.15g, "
        "\"ceil_3\": %.15g, \"ceil_default\": %.15g, \"ceilTwoDecimals\": %.15g}",
        Precision_ceil(testValue, 0),
        Precision_ceil(testValue, 1),
        Precision_ceil(testValue, 2),
        Precision_ceil(testValue, 3),
        Precision_ceil(testValue),  // Default parameter (should be 0)
        Precision_ceilTwoDecimals(testValue));
}

void test_distance_based_functions(TestResult* result) {
    double testValue = 123.7;
    double distances[] = {1, 5, 10, 0.5};
    int numDistances = sizeof(distances) / sizeof(distances[0]);
    
    strcpy(result->result, "[");
    for (int i = 0; i < numDistances; i++) {
        double distance = distances[i];
        char distanceStr[200];
        snprintf(distanceStr, sizeof(distanceStr),
            "{\"distance\": %.15g, \"ceilDistance\": %.15g, "
            "\"roundDistance\": %.15g, \"floorDistance\": %.15g}%s",
            distance,
            Precision_ceilDistance(testValue, distance),
            Precision_roundDistance(testValue, distance),
            Precision_floorDistance(testValue, distance),
            (i < numDistances - 1) ? ", " : "");
        strcat(result->result, distanceStr);
    }
    strcat(result->result, "]");
}

void test_negative_numbers_handling(TestResult* result) {
    double negativeValue = -123.456789;
    
    snprintf(result->result, sizeof(result->result),
        "{\"fix\": %.15g, \"round_2\": %.15g, \"floor_2\": %.15g, "
        "\"ceil_2\": %.15g, \"roundDistance_5\": %.15g, \"ceilDistance_5\": %.15g, "
        "\"floorDistance_5\": %.15g}",
        Precision_fix(negativeValue),
        Precision_round(negativeValue, 2),
        Precision_floor(negativeValue, 2),
        Precision_ceil(negativeValue, 2),
        Precision_roundDistance(negativeValue, 5),
        Precision_ceilDistance(negativeValue, 5),
        Precision_floorDistance(negativeValue, 5));
}

void test_zero_and_small_numbers(TestResult* result) {
    double smallPositive = 0.000001;
    double smallNegative = -0.000001;
    double zero = 0.0;
    
    snprintf(result->result, sizeof(result->result),
        "{\"zero\": {\"fix\": %.15g, \"round_2\": %.15g, \"floor_2\": %.15g, \"ceil_2\": %.15g}, "
        "\"small_positive\": {\"fix\": %.15g, \"round_2\": %.15g, \"floor_2\": %.15g, \"ceil_2\": %.15g}, "
        "\"small_negative\": {\"fix\": %.15g, \"round_2\": %.15g, \"floor_2\": %.15g, \"ceil_2\": %.15g}}",
        Precision_fix(zero), Precision_round(zero, 2), Precision_floor(zero, 2), Precision_ceil(zero, 2),
        Precision_fix(smallPositive), Precision_round(smallPositive, 2), Precision_floor(smallPositive, 2), Precision_ceil(smallPositive, 2),
        Precision_fix(smallNegative), Precision_round(smallNegative, 2), Precision_floor(smallNegative, 2), Precision_ceil(smallNegative, 2));
}

void test_distance_function_edge_cases(TestResult* result) {
    snprintf(result->result, sizeof(result->result),
        "{\"zero_distance_1\": {\"ceil\": %.15g, \"round\": %.15g, \"floor\": %.15g}, "
        "\"exact_multiple\": {\"value\": %.15g, \"distance\": %.15g, \"ceil\": %.15g, \"round\": %.15g, \"floor\": %.15g}, "
        "\"fractional_distance\": {\"value\": %.15g, \"distance\": %.15g, \"ceil\": %.15g, \"round\": %.15g, \"floor\": %.15g}}",
        Precision_ceilDistance(0, 1), Precision_roundDistance(0, 1), Precision_floorDistance(0, 1),
        10.0, 5.0, Precision_ceilDistance(10.0, 5.0), Precision_roundDistance(10.0, 5.0), Precision_floorDistance(10.0, 5.0),
        1.7, 0.25, Precision_ceilDistance(1.7, 0.25), Precision_roundDistance(1.7, 0.25), Precision_floorDistance(1.7, 0.25));
}

void test_large_numbers_precision(TestResult* result) {
    double largeNumber = 1234567890.123456789;
    
    snprintf(result->result, sizeof(result->result),
        "{\"fix\": %.15g, \"round_0\": %.15g, \"round_2\": %.15g, "
        "\"floor_2\": %.15g, \"ceil_2\": %.15g}",
        Precision_fix(largeNumber),
        Precision_round(largeNumber, 0),
        Precision_round(largeNumber, 2),
        Precision_floor(largeNumber, 2),
        Precision_ceil(largeNumber, 2));
}

void test_default_parameter_verification(TestResult* result) {
    double testValue = 123.456789;
    
    double round_no_digits = Precision_round(testValue);
    double floor_no_digits = Precision_floor(testValue);
    double ceil_no_digits = Precision_ceil(testValue);
    double round_explicit_0 = Precision_round(testValue, 0);
    double floor_explicit_0 = Precision_floor(testValue, 0);
    double ceil_explicit_0 = Precision_ceil(testValue, 0);
    
    snprintf(result->result, sizeof(result->result),
        "{\"round_no_digits\": %.15g, \"floor_no_digits\": %.15g, \"ceil_no_digits\": %.15g, "
        "\"round_explicit_0\": %.15g, \"floor_explicit_0\": %.15g, \"ceil_explicit_0\": %.15g, "
        "\"round_equal\": %s, \"floor_equal\": %s, \"ceil_equal\": %s}",
        round_no_digits, floor_no_digits, ceil_no_digits,
        round_explicit_0, floor_explicit_0, ceil_explicit_0,
        (round_no_digits == round_explicit_0) ? "true" : "false",
        (floor_no_digits == floor_explicit_0) ? "true" : "false",
        (ceil_no_digits == ceil_explicit_0) ? "true" : "false");
}

int main() {
    printf("=== Precision C Tests ===\n");
    
    // Run all tests
    runTest("Precision_Fix_Function", test_precision_fix_function);
    runTest("Round_Functions_Various_Digits", test_round_functions_various_digits);
    runTest("Floor_Functions_Various_Digits", test_floor_functions_various_digits);
    runTest("Ceil_Functions_Various_Digits", test_ceil_functions_various_digits);
    runTest("Distance_Based_Functions", test_distance_based_functions);
    runTest("Negative_Numbers_Handling", test_negative_numbers_handling);
    runTest("Zero_And_Small_Numbers", test_zero_and_small_numbers);
    runTest("Distance_Function_Edge_Cases", test_distance_function_edge_cases);
    runTest("Large_Numbers_Precision", test_large_numbers_precision);
    runTest("Default_Parameter_Verification", test_default_parameter_verification);
    
    // Summary
    int passed = 0;
    for (int i = 0; i < testCount; i++) {
        if (testResults[i].success) passed++;
    }
    
    printf("\n=== Precision C Test Summary ===\n");
    printf("Total Tests: %d\n", testCount);
    printf("Passed: %d\n", passed);
    printf("Failed: %d\n", testCount - passed);
    
    if (passed < testCount) {
        printf("\nFailed Tests:\n");
        for (int i = 0; i < testCount; i++) {
            if (!testResults[i].success) {
                printf("- %s: %s\n", testResults[i].testName, testResults[i].error);
            }
        }
    }
    
    return (passed == testCount) ? 0 : 1;
}