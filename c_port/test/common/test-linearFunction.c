#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "../../include/common/linearFunction.h"

/**
 * C test file for linearFunction module
 * Tests the C port implementation 
 * This test file should produce identical results to test-LinearFunction.ts
 */

// Test results structure
typedef struct {
    char testName[100];
    char result[3000];  // Large buffer for complex results
    int success;
    char error[200];
} TestResult;

TestResult testResults[15];
int testCount = 0;

void runTest(const char* testName, void (*testFunc)(TestResult*)) {
    TestResult* result = &testResults[testCount];
    strcpy(result->testName, testName);
    result->success = 1;
    result->error[0] = '\0';
    
    printf("[C] %s: ", testName);
    testFunc(result);
    
    if (result->success) {
        printf("%s\n", result->result);
    } else {
        printf("ERROR - %s\n", result->error);
    }
    
    testCount++;
}

void test_speed_from_ranges(TestResult* result) {
    Range testCases_x[] = {
        {0, 10},
        {5, 15},
        {-5, 5},
        {0, 1},
        {10, 10}
    };
    Range testCases_y[] = {
        {0, 20},
        {100, 200},
        {-10, 10},
        {0, 0},
        {5, 15}
    };
    int numCases = 5;
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double speed = LinearFunction_speed(testCases_x[i], testCases_y[i]);
        char caseStr[300];
        snprintf(caseStr, sizeof(caseStr),
            "{\"case\": %d, \"x\": {\"start\": %.15g, \"end\": %.15g}, "
            "\"y\": {\"start\": %.15g, \"end\": %.15g}, \"speed\": %.15g}%s",
            i + 1, testCases_x[i].start, testCases_x[i].end,
            testCases_y[i].start, testCases_y[i].end, speed,
            (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_speed_by_x_change(TestResult* result) {
    double testCases[][3] = {
        {0, 10, 5},     // yStart, yEnd, xChange
        {100, 200, 10},
        {-50, 50, 20},
        {10, 10, 5},
        {0, 10, 0}
    };
    int numCases = 5;
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double yStart = testCases[i][0];
        double yEnd = testCases[i][1];
        double xChange = testCases[i][2];
        double speed = LinearFunction_speedByXChange(yStart, yEnd, xChange);
        char caseStr[250];
        snprintf(caseStr, sizeof(caseStr),
            "{\"case\": %d, \"yStart\": %.15g, \"yEnd\": %.15g, "
            "\"xChange\": %.15g, \"speed\": %.15g}%s",
            i + 1, yStart, yEnd, xChange, speed,
            (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_y_value_at_x_change(TestResult* result) {
    double testCases[][3] = {
        {0, 2, 5},      // yStart, speed, xChange
        {100, -3, 10},
        {50, 0, 20},
        {-10, 1.5, -4},
        {0, 0.5, 0}
    };
    int numCases = 5;
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double yStart = testCases[i][0];
        double speed = testCases[i][1];
        double xChange = testCases[i][2];
        double yValue = LinearFunction_yValueAt(yStart, speed, xChange);
        char caseStr[250];
        snprintf(caseStr, sizeof(caseStr),
            "{\"case\": %d, \"yStart\": %.15g, \"speed\": %.15g, "
            "\"xChange\": %.15g, \"yValue\": %.15g}%s",
            i + 1, yStart, speed, xChange, yValue,
            (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_x_value_at_absolute(TestResult* result) {
    Range testCases_x[] = {
        {0, 10},
        {5, 15},
        {0, 1},
        {-5, 5}
    };
    Range testCases_y[] = {
        {0, 20},
        {100, 200},
        {10, 10},
        {-10, 10}
    };
    double yValues[] = {10, 150, 10, 0};
    int numCases = 4;
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double xAbsolute = LinearFunction_xValueAtAbsolute(testCases_x[i], testCases_y[i], yValues[i]);
        char caseStr[400];
        snprintf(caseStr, sizeof(caseStr),
            "{\"case\": %d, \"x\": {\"start\": %.15g, \"end\": %.15g}, "
            "\"y\": {\"start\": %.15g, \"end\": %.15g}, \"yValue\": %.15g, \"xAbsolute\": %.15g}%s",
            i + 1, testCases_x[i].start, testCases_x[i].end,
            testCases_y[i].start, testCases_y[i].end, yValues[i], xAbsolute,
            (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_x_value_at_relative(TestResult* result) {
    double testCases[][3] = {
        {0, 2, 10},     // yStart, speed, yValue
        {100, -5, 50},
        {50, 0, 50},
        {50, 0, 60},
        {-10, 0.5, 0},
        {0, 1, 0}
    };
    int numCases = 6;
    
    strcpy(result->result, "[");
    for (int i = 0; i < numCases; i++) {
        double yStart = testCases[i][0];
        double speed = testCases[i][1];
        double yValue = testCases[i][2];
        double xRelative = LinearFunction_xValueAt(yStart, speed, yValue);
        char caseStr[250];
        snprintf(caseStr, sizeof(caseStr),
            "{\"case\": %d, \"yStart\": %.15g, \"speed\": %.15g, "
            "\"yValue\": %.15g, \"xRelative\": %.15g}%s",
            i + 1, yStart, speed, yValue, xRelative,
            (i < numCases - 1) ? ", " : "");
        strcat(result->result, caseStr);
    }
    strcat(result->result, "]");
}

void test_zero_speed_edge_cases(TestResult* result) {
    double xValue1 = LinearFunction_xValueAt(10, 0, 10);
    double xValue2 = LinearFunction_xValueAt(10, 0, 20);
    Range x = {0, 10};
    Range y = {5, 5};
    double xAbsolute = LinearFunction_xValueAtAbsolute(x, y, 5);
    
    snprintf(result->result, sizeof(result->result),
        "[{\"description\": \"Zero speed, same y value\", \"yStart\": 10, \"speed\": 0, "
        "\"yValue\": 10, \"xValue\": %.15g}, "
        "{\"description\": \"Zero speed, different y value\", \"yStart\": 10, \"speed\": 0, "
        "\"yValue\": 20, \"xValue\": %.15g}, "
        "{\"description\": \"Zero speed from horizontal range\", \"x\": {\"start\": 0, \"end\": 10}, "
        "\"y\": {\"start\": 5, \"end\": 5}, \"yValue\": 5, \"xAbsolute\": %.15g}]",
        xValue1, xValue2, xAbsolute);
}

void test_negative_values_handling(TestResult* result) {
    Range x1 = {-10, -5};
    Range y1 = {-20, -10};
    double speed1 = LinearFunction_speed(x1, y1);
    double yValue1 = LinearFunction_yValueAt(-20, speed1, 3);
    
    Range x2 = {-5, 5};
    Range y2 = {-10, 10};
    double speed2 = LinearFunction_speed(x2, y2);
    double xAbsolute2 = LinearFunction_xValueAtAbsolute(x2, y2, 0);
    
    snprintf(result->result, sizeof(result->result),
        "[{\"description\": \"Negative ranges\", \"x\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"y\": {\"start\": %.15g, \"end\": %.15g}, \"speed\": %.15g, \"yValue\": %.15g}, "
        "{\"description\": \"Mixed positive/negative\", \"x\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"y\": {\"start\": %.15g, \"end\": %.15g}, \"speed\": %.15g, \"xAbsolute\": %.15g}]",
        x1.start, x1.end, y1.start, y1.end, speed1, yValue1,
        x2.start, x2.end, y2.start, y2.end, speed2, xAbsolute2);
}

void test_large_number_precision(TestResult* result) {
    Range largeX = {1000000, 2000000};
    Range largeY = {5000000, 6000000};
    double speed = LinearFunction_speed(largeX, largeY);
    double yAt500000 = LinearFunction_yValueAt(largeY.start, speed, 500000);
    double xAtMidpoint = LinearFunction_xValueAtAbsolute(largeX, largeY, 5500000);
    
    snprintf(result->result, sizeof(result->result),
        "{\"x\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"y\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"speed\": %.15g, \"yAt500000\": %.15g, \"xAtMidpoint\": %.15g}",
        largeX.start, largeX.end, largeY.start, largeY.end, speed, yAt500000, xAtMidpoint);
}

void test_small_number_precision(TestResult* result) {
    Range smallX = {0.0001, 0.0002};
    Range smallY = {0.0005, 0.0006};
    double speed = LinearFunction_speed(smallX, smallY);
    double yAt0_00005 = LinearFunction_yValueAt(smallY.start, speed, 0.00005);
    double xAtMidpoint = LinearFunction_xValueAtAbsolute(smallX, smallY, 0.00055);
    
    snprintf(result->result, sizeof(result->result),
        "{\"x\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"y\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"speed\": %.15g, \"yAt0_00005\": %.15g, \"xAtMidpoint\": %.15g}",
        smallX.start, smallX.end, smallY.start, smallY.end, speed, yAt0_00005, xAtMidpoint);
}

void test_method_consistency(TestResult* result) {
    Range x = {10, 20};
    Range y = {50, 100};
    double speed = LinearFunction_speed(x, y);
    double speedByChange = LinearFunction_speedByXChange(y.start, y.end, x.end - x.start);
    
    int speedsMatch = (fabs(speed - speedByChange) < 1e-10) ? 1 : 0;
    
    double testYValue = 75;
    double xAbsolute = LinearFunction_xValueAtAbsolute(x, y, testYValue);
    double xRelative = LinearFunction_xValueAt(y.start, speed, testYValue);
    double xCalculated = x.start + xRelative;
    int xValuesMatch = (fabs(xAbsolute - xCalculated) < 1e-10) ? 1 : 0;
    
    snprintf(result->result, sizeof(result->result),
        "{\"x\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"y\": {\"start\": %.15g, \"end\": %.15g}, "
        "\"speed\": %.15g, \"speedByChange\": %.15g, \"speedsMatch\": %s, "
        "\"testYValue\": %.15g, \"xAbsolute\": %.15g, \"xRelative\": %.15g, "
        "\"xCalculated\": %.15g, \"xValuesMatch\": %s}",
        x.start, x.end, y.start, y.end, speed, speedByChange, 
        speedsMatch ? "true" : "false", testYValue, xAbsolute, xRelative, 
        xCalculated, xValuesMatch ? "true" : "false");
}

int main() {
    printf("=== LinearFunction C Tests ===\n");
    
    // Run all tests
    runTest("Speed_From_Ranges", test_speed_from_ranges);
    runTest("Speed_By_X_Change", test_speed_by_x_change);
    runTest("Y_Value_At_X_Change", test_y_value_at_x_change);
    runTest("X_Value_At_Absolute", test_x_value_at_absolute);
    runTest("X_Value_At_Relative", test_x_value_at_relative);
    runTest("Zero_Speed_Edge_Cases", test_zero_speed_edge_cases);
    runTest("Negative_Values_Handling", test_negative_values_handling);
    runTest("Large_Number_Precision", test_large_number_precision);
    runTest("Small_Number_Precision", test_small_number_precision);
    runTest("Method_Consistency", test_method_consistency);
    
    // Summary
    int passed = 0;
    for (int i = 0; i < testCount; i++) {
        if (testResults[i].success) passed++;
    }
    
    printf("\n=== LinearFunction C Test Summary ===\n");
    printf("Total Tests: %d\n", testCount);
    printf("Passed: %d\n", passed);
    printf("Failed: %d\n", testCount - passed);
    
    if (passed < testCount) {
        printf("\nFailed Tests:\n");
        for (int i = 0; i < testCount; i++) {
            if (!testResults[i].success) {
                printf("- %s: %s\n", testResults[i].testName, testResults[i].error);
            }
        }
    }
    
    return (passed == testCount) ? 0 : 1;
}