import { Precision } from '../../../projects/scuba-physics/src/lib/common/precision';

/**
 * TypeScript test file for Precision module
 * Tests the original TypeScript implementation 
 * This test file should produce identical results to test-precision.c
 */

// Test results storage
interface TestResult {
    testName: string;
    result: any;
    success: boolean;
    error?: string;
}

const testResults: TestResult[] = [];

function runTest(testName: string, testFunc: () => any): void {
    try {
        const result = testFunc();
        testResults.push({
            testName,
            result,
            success: true
        });
        console.log(`[TS] ${testName}: ${JSON.stringify(result)}`);
    } catch (error) {
        testResults.push({
            testName,
            result: null,
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
        console.log(`[TS] ${testName}: ERROR - ${error}`);
    }
}

function main(): void {
    console.log('=== Precision TypeScript Tests ===');
    
    // Test 1: Precision.fix function
    runTest('Precision_Fix_Function', () => {
        const testCases = [
            1.23456789123456789,
            0.1 + 0.2,  // Classic JavaScript precision issue
            3.141592653589793238,
            -2.718281828459045,
            0.0000000001,
            1000000000.123456789,
            0.0
        ];
        
        return testCases.map(value => ({
            input: value,
            output: Precision.fix(value)
        }));
    });
    
    // Test 2: Round functions with different digits
    runTest('Round_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            round_0: Precision.round(testValue, 0),
            round_1: Precision.round(testValue, 1),
            round_2: Precision.round(testValue, 2),
            round_3: Precision.round(testValue, 3),
            round_default: Precision.round(testValue), // Should default to 0
            roundTwoDecimals: Precision.roundTwoDecimals(testValue)
        };
    });
    
    // Test 3: Floor functions with different digits
    runTest('Floor_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            floor_0: Precision.floor(testValue, 0),
            floor_1: Precision.floor(testValue, 1),
            floor_2: Precision.floor(testValue, 2),
            floor_3: Precision.floor(testValue, 3),
            floor_default: Precision.floor(testValue), // Should default to 0
            floorTwoDecimals: Precision.floorTwoDecimals(testValue)
        };
    });
    
    // Test 4: Ceil functions with different digits
    runTest('Ceil_Functions_Various_Digits', () => {
        const testValue = 123.456789;
        return {
            ceil_0: Precision.ceil(testValue, 0),
            ceil_1: Precision.ceil(testValue, 1),
            ceil_2: Precision.ceil(testValue, 2),
            ceil_3: Precision.ceil(testValue, 3),
            ceil_default: Precision.ceil(testValue), // Should default to 0
            ceilTwoDecimals: Precision.ceilTwoDecimals(testValue)
        };
    });
    
    // Test 5: Distance-based functions
    runTest('Distance_Based_Functions', () => {
        const testValue = 123.7;
        const distances = [1, 5, 10, 0.5];
        
        return distances.map(distance => ({
            distance,
            ceilDistance: Precision.ceilDistance(testValue, distance),
            roundDistance: Precision.roundDistance(testValue, distance),
            floorDistance: Precision.floorDistance(testValue, distance)
        }));
    });
    
    // Test 6: Negative numbers handling
    runTest('Negative_Numbers_Handling', () => {
        const negativeValue = -123.456789;
        return {
            fix: Precision.fix(negativeValue),
            round_2: Precision.round(negativeValue, 2),
            floor_2: Precision.floor(negativeValue, 2),
            ceil_2: Precision.ceil(negativeValue, 2),
            roundDistance_5: Precision.roundDistance(negativeValue, 5),
            ceilDistance_5: Precision.ceilDistance(negativeValue, 5),
            floorDistance_5: Precision.floorDistance(negativeValue, 5)
        };
    });
    
    // Test 7: Zero and very small numbers
    runTest('Zero_And_Small_Numbers', () => {
        const smallPositive = 0.000001;
        const smallNegative = -0.000001;
        const zero = 0.0;
        
        return {
            zero: {
                fix: Precision.fix(zero),
                round_2: Precision.round(zero, 2),
                floor_2: Precision.floor(zero, 2),
                ceil_2: Precision.ceil(zero, 2)
            },
            small_positive: {
                fix: Precision.fix(smallPositive),
                round_2: Precision.round(smallPositive, 2),
                floor_2: Precision.floor(smallPositive, 2),
                ceil_2: Precision.ceil(smallPositive, 2)
            },
            small_negative: {
                fix: Precision.fix(smallNegative),
                round_2: Precision.round(smallNegative, 2),
                floor_2: Precision.floor(smallNegative, 2),
                ceil_2: Precision.ceil(smallNegative, 2)
            }
        };
    });
    
    // Test 8: Edge cases with distance functions
    runTest('Distance_Function_Edge_Cases', () => {
        return {
            zero_distance_1: {
                ceil: Precision.ceilDistance(0, 1),
                round: Precision.roundDistance(0, 1),
                floor: Precision.floorDistance(0, 1)
            },
            exact_multiple: {
                value: 10.0,
                distance: 5.0,
                ceil: Precision.ceilDistance(10.0, 5.0),
                round: Precision.roundDistance(10.0, 5.0),
                floor: Precision.floorDistance(10.0, 5.0)
            },
            fractional_distance: {
                value: 1.7,
                distance: 0.25,
                ceil: Precision.ceilDistance(1.7, 0.25),
                round: Precision.roundDistance(1.7, 0.25),
                floor: Precision.floorDistance(1.7, 0.25)
            }
        };
    });
    
    // Test 9: Large numbers precision
    runTest('Large_Numbers_Precision', () => {
        const largeNumber = 1234567890.123456789;
        return {
            fix: Precision.fix(largeNumber),
            round_0: Precision.round(largeNumber, 0),
            round_2: Precision.round(largeNumber, 2),
            floor_2: Precision.floor(largeNumber, 2),
            ceil_2: Precision.ceil(largeNumber, 2)
        };
    });
    
    // Test 10: Default parameter verification
    runTest('Default_Parameter_Verification', () => {
        const testValue = 123.456789;
        return {
            // Test that default parameters work correctly
            round_no_digits: Precision.round(testValue),
            floor_no_digits: Precision.floor(testValue),
            ceil_no_digits: Precision.ceil(testValue),
            // Compare with explicit 0
            round_explicit_0: Precision.round(testValue, 0),
            floor_explicit_0: Precision.floor(testValue, 0),
            ceil_explicit_0: Precision.ceil(testValue, 0),
            // Verify they are equal
            round_equal: Precision.round(testValue) === Precision.round(testValue, 0),
            floor_equal: Precision.floor(testValue) === Precision.floor(testValue, 0),
            ceil_equal: Precision.ceil(testValue) === Precision.ceil(testValue, 0)
        };
    });
    
    // Summary
    const totalTests = testResults.length;
    const passedTests = testResults.filter(r => r.success).length;
    console.log('\n=== Precision TypeScript Test Summary ===');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    
    if (passedTests < totalTests) {
        console.log('\nFailed Tests:');
        testResults.filter(r => !r.success).forEach(result => {
            console.log(`- ${result.testName}: ${result.error}`);
        });
    }
}

// Run tests
main();

// Export results for comparison
export { testResults };