# Contributing

No extra rules are applied. Just follow the general rules of contributing to a project and linter settings.
<PERSON><PERSON> <PERSON><PERSON> requests to the develop branch and link issue the PR solves.


## List of contributors

* [<PERSON>](https://github.com/m<PERSON><PERSON>) - CNS and OTU calculations
* [<PERSON><PERSON>](https://github.com/josef<PERSON>) - All kinds of automated tests
* [<PERSON>](https://github.com/marktai) - UI improvements

## Bachelor/Master's thesis

* [<PERSON>](https://github.com/xlebod) - Profiles comparison


