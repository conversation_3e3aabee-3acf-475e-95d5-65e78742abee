{"name": "planner", "version": "0.1.37", "license": "MIT", "scripts": {"start": "ng build scuba-physics && ng serve", "start-pwa": "ng build --configuration production planner && npx http-server -p 9090 -c-1 dist/planner", "build": "ng build --configuration production planner", "build-lib": "ng build --configuration production scuba-physics", "test": "ng test", "test-ci": "ng test --project planner --browsers=ChromeHeadless --no-watch", "test-lib": "ng test --project scuba-physics", "test-lib-ci": "ng test --project scuba-physics --browsers=ChromeHeadless --no-watch", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/common": "^19.2.8", "@angular/compiler": "^19.2.8", "@angular/core": "^19.2.8", "@angular/forms": "^19.2.8", "@angular/localize": "^19.2.8", "@angular/platform-browser": "^19.2.8", "@angular/platform-browser-dynamic": "^19.2.8", "@angular/router": "^19.2.8", "@angular/service-worker": "^19.2.8", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@maskito/angular": "^3.7.2", "@maskito/kit": "^3.7.2", "@popperjs/core": "^2.11.8", "add-to-homescreen": "github:docluv/add-to-homescreen", "canvas-confetti": "^1.9.3", "lodash": "^4.17.21", "mdb-angular-ui-kit": "^8.0.0", "ngx-clipboard": "^16.0.0", "ngx-md": "^19.0.0", "plotly.js-dist": "^2.35.3", "rxjs": "^7.8.2", "tslib": "^2.7.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/schematics": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.8", "@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@playwright/test": "^1.52.0", "@types/canvas-confetti": "^1.9.0", "@types/jasmine": "^5.1.7", "@types/lodash": "^4.17.16", "@types/marked": "^5.0.2", "@types/node": "^22.15.3", "@types/plotly.js": "^2.35.5", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-plugin-html": "^8.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jasmine": "^4.2.2", "globals": "^16.2.0", "http-server": "^14.1.1", "jasmine-core": "^5.7.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "karma-junit-reporter": "^2.0.1", "karma-verbose-reporter": "^0.0.8", "ng-packagr": "^19.2.2", "playwright-ng-schematics": "^1.3.1", "typescript": "^5.8.3"}}