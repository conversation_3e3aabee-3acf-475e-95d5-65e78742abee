{"version": "2.0.0", "tasks": [{"type": "npm", "script": "start", "isBackground": true, "presentation": {"focus": true, "panel": "dedicated"}, "group": {"kind": "build", "isDefault": true}, "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${cwd}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "Compiled |Failed to compile."}}}}, {"type": "npm", "script": "test", "label": "npm: test", "detail": "ng test", "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${cwd}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "Compiled |Failed to compile."}}}}, {"type": "npm", "script": "test-lib", "label": "npm: test-lib", "detail": "ng test --project scuba-physics", "problemMatcher": {"owner": "typescript", "source": "ts", "applyTo": "closedDocuments", "fileLocation": ["relative", "${cwd}"], "pattern": "$tsc", "background": {"activeOnStart": true, "beginsPattern": {"regexp": "(.*?)"}, "endsPattern": {"regexp": "Compiled |Failed to compile."}}}}]}