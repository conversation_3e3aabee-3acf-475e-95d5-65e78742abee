{
    "eslint.options": {
        "extensions": [
            ".ts",
            ".html"
        ]
    },
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact",
        "html"
    ],
    "karmaTestExplorer.testFiles": [
        "**/*{.,-,_}{test,spec}.{ts,js}",
        "**/{test,spec}{.,-,_}*.{ts,js}"
    ],
    "cSpell.words": [
        "inputcontrols",
        "Nitrox"
    ],
    }
