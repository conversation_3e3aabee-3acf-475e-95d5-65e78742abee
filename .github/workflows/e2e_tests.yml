name: E2E Tests
on:
  push:
    branches:
    - develop
    - master
  pull_request:
    branches:
    - develop
    - master

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - uses: actions/setup-node@v4
      with:
        node-version-file: .nvmrc

    - name: Install dependencies
      run: npm ci

    - run: npm run build-lib
    - run: npm install ./dist/scuba-physics --no-package-lock

    - name: Install Playwright Browsers
      run: npx playwright install chromium

    - name: Run E2E tests
      run: npm run e2e

    - name: Publish Test Results
      uses: EnricoMi/publish-unit-test-result-action@v2.18.0
      if: always()
      with:
        files: |
            test-results/e2e.xml
