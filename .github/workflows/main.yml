name: CI

on:
  push:
    branches:
    - '*'

  pull_request:
    branches:
    - '*'

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2

      - name: Setup NodeJs
        uses: actions/setup-node@v4.1.0
        with:
           node-version-file: .nvmrc

      - run: npm ci

      - run: npm run test-lib-ci --configuration=ci

      - run: npm run build-lib

      - run: npm install ./dist/scuba-physics --no-package-lock

      - run: npm run test-ci --configuration=ci

      - name: Publish Test Results
        uses: EnricoMi/publish-unit-test-result-action@v2.18.0
        if: always()
        with:
          files: |
            test-results/**/*.xml

