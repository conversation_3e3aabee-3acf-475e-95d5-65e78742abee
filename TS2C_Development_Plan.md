# TypeScript到C语言移植开发计划 (TS2C)

## 项目概述

### 项目目标
将 `projects/scuba-physics/src/lib` 下的所有TypeScript源码精确移植为C语言，确保相同输入下C函数与TypeScript函数运行结果完全一致。

### 移植范围
- **源文件总数**: 86个TypeScript文件 (46个源文件 + 40个测试文件)
- **模块总数**: 7个主要模块 (common, physics, gases, depths, consumption, calculators, algorithm)
- **核心库**: 潜水物理学计算库，包含Bühlmann ZHL-16C减压算法

### 唯一验收标准
- **数值一致性**: 相同输入下，C语言函数运行结果与TypeScript函数运行结果一致
- **精度容忍**: 允许仅限于语言差异导致的极小数值误差，误差判定严格遵循精度契约
- **功能完整性**: 所有公开API函数完整移植，保持相同的行为特征

---

## 移植架构设计

### 目录结构
```
c_port/
├── src/                    # C源代码
├── include/               # C头文件
├── test/                  # 对照测试 (C测试 + TS基准测试)
└── reports/               # 质量报告 (reviewer/tester/quality)
```

### 模块依赖关系
```
基础层: common, physics
  ↓
业务层: gases, depths, consumption  
  ↓
应用层: calculators
  ↓
核心层: algorithm (最复杂)
```

---

## 精度契约与移植约束

### 数值精度契约
- **数值类型**: C侧统一使用double，严禁使用float
- **比较策略**: 提供epsilon比较函数，默认eps=1e-12
- **误差阈值**: 对照测试默认绝对/相对误差 ≤ 1e-9
- **物理常数**: 与TypeScript源码保持完全一致的精度

### 核心移植约束
- **完全精确移植**: 禁止任何算法优化，保持TS原始逻辑不变
- **命名一致性**: 函数/变量名与TS保持一致 (符合C语言规范)
- **注释完整移植**: 英文注释与链接原样保留
- **控制流一致**: 分支/循环/边界处理与TS完全相同
- **数据流一致**: 计算顺序、舍入/截断时机与TS一致

### C语言适配策略
- **接口多态**: 用函数指针结构体模拟TypeScript接口
- **回调函数**: 用函数指针参数实现TypeScript回调
- **动态数组**: 实现通用动态数据结构
- **内存管理**: 严格的内存分配和释放策略
- **错误处理**: 用返回码替代TypeScript异常机制

---

## 移植分级策略

### 复杂度分级
- **简单文件**: 常量定义、数据结构、简单工具函数 → 可批量移植
- **中等文件**: 单元转换、基础计算、业务逻辑 → 单个移植
- **复杂文件**: 核心算法、搜索算法、复合结构 → 重点单独处理

### 移植优先级
1. **依赖优先**: 被依赖模块优先移植 (common → physics → gases/depths/consumption → calculators → algorithm)
2. **简单优先**: 同一模块内简单文件优先，复杂文件后处理
3. **关键优先**: 核心算法和基础工具优先于辅助功能

### 模块移植顺序
```
阶段1: common(4个源文件) + physics(5个源文件)           → 基础层
阶段2: gases(7个源文件) + depths(4个源文件) + consumption(5个源文件) → 业务层  
阶段3: calculators(9个源文件)                          → 应用层
阶段4: algorithm(12个源文件)                           → 核心层
阶段5: 集成测试与优化                                    → 验证层
```

---

## 子代理协作体系

### 专业子代理角色
1. **TS-Source Analyst**: 结构分析，复杂度评估，移植范围确定
2. **C-Code Porter**: 精确移植实施，移植清单维护
3. **TS-to-C Code Reviewer**: 逐行对比审查，差异分析
4. **TypeScript-C Port Tester**: 对照测试生成和执行
5. **Test Quality Reviewer**: 测试覆盖率和质量验证

### 工作流程
**每轮对话内完成一个完整移植闭环**: **Porter → Reviewer → Tester → Quality**

### 单轮对话实施流程
每轮对话按以下顺序调用4个子代理，完成一批文件的移植验收：

1. **调用C-Code Porter**: 精确移植确定的文件  
2. **调用TS-to-C Code Reviewer**: 逐行审查移植代码，确保完全一致
3. **调用TypeScript-C Port Tester**: 生成对照测试，验证功能和精度
4. **调用Test Quality Reviewer**: 审查测试工作完成度和质量，确认测试标准符合要求

### 特殊情况处理
- **审查不通过**: 立即通知C-Code Porter进行问题修改，重新审查
- **测试不通过**: 立即通知C-Code Porter进行问题修改，重新测试  
- **测试质量不达标**: 立即通知TypeScript-C Port Tester补充测试用例或修复测试问题
- **TS-Source Analyst**: 仅在需要时调用，不是每轮必须调用

**完成一轮后**: 用户发起下一轮对话，继续下一批文件的移植任务

---

## 移植质量保证

### 代码审查标准
- 命名一致性、常量精度一致性
- 控制流和数据流完全对应
- 注释和文档完整移植
- 无遗漏的代码段或函数

### 测试验证策略
- **成对对照测试**: C测试与TS基准测试
- **精度契约验证**: 数值误差范围检查
- **边界条件测试**: 极值和异常输入处理
- **功能一致性验证**: API行为完全匹配

### 质量验证指标
- **代码审查通过率**: 100%
- **测试覆盖率**: ≥ 95%
- **精度验证通过率**: 100%
- **功能一致性验证**: 100%

---

## 技术挑战与解决方案

### 主要技术挑战
1. **接口多态性**: TypeScript接口在C中的实现
2. **回调函数管理**: 复杂回调机制的C语言适配
3. **精度敏感计算**: 浮点运算精度的跨语言一致性
4. **动态数据结构**: TypeScript动态特性的C实现
5. **内存安全**: C语言手动内存管理

### 关键算法重点
- **BuhlmannAlgorithm**: 最复杂的减压计算核心
- **BinaryIntervalSearch**: 复杂搜索算法和回调机制
- **Tissues计算**: 组织饱和度计算的精度敏感性

---

## 开发环境要求

### 编译环境
- **C标准**: C99标准 (便于跨平台移植)
- **编译器**: GCC 4.9+ 或 Clang 3.9+
- **编译选项**: 启用所有警告，优化级别适中

### 测试框架
- **C单元测试**: 选择合适的C测试框架
- **TS对照测试**: Node.js + TypeScript环境
- **性能测试**: 性能分析工具

### 质量工具
- **静态分析**: 代码质量检查工具
- **内存检查**: 内存泄漏和安全检查
- **代码覆盖**: 测试覆盖率分析

---

## 项目里程碑

### 详细分轮对话移植计划

#### 第一阶段: 基础模块移植 (第1-4轮)
**目标**: 建立基础工具库和物理计算支撑

**第1轮** - Common模块基础 (简单文件批量移植):
- `projects/scuba-physics/src/lib/common/featureFlags.ts` (简单)
- `projects/scuba-physics/src/lib/common/precision.ts` (简单)  
- `projects/scuba-physics/src/lib/common/linearFunction.ts` (简单)

**第2轮** - Common模块算法 (中等文件单独移植):
- `projects/scuba-physics/src/lib/common/BinaryIntervalSearch.ts` (中等)

**第3轮** - Physics模块基础:
- `projects/scuba-physics/src/lib/physics/Time.ts` (简单)
- `projects/scuba-physics/src/lib/physics/pressure-converter.ts` (中等)

**第4轮** - Physics模块核心:
- `projects/scuba-physics/src/lib/physics/units.ts` (中等)
- `projects/scuba-physics/src/lib/physics/depth-converter.ts` (中等)

#### 第二阶段: 气体管理模块 (第5-8轮)
**目标**: 实现气体计算和管理功能

**第5轮** - Physics模块复杂算法:
- `projects/scuba-physics/src/lib/physics/compressibility.ts` (复杂)

**第6轮** - Gases模块简单文件批量:
- `projects/scuba-physics/src/lib/gases/GasNames.ts` (简单)
- `projects/scuba-physics/src/lib/gases/gas.properties.ts` (简单)
- `projects/scuba-physics/src/lib/gases/gasToxicity.ts` (简单)

**第7轮** - Gases模块中等文件:
- `projects/scuba-physics/src/lib/gases/GasMixtures.ts` (中等)
- `projects/scuba-physics/src/lib/gases/StandardGases.ts` (中等)
- `projects/scuba-physics/src/lib/gases/GasDensity.ts` (中等)

**第8轮** - Gases模块复杂算法:
- `projects/scuba-physics/src/lib/gases/Gases.ts` (复杂)

#### 第三阶段: 潜水计划模块 (第9-13轮)
**目标**: 实现深度管理和消耗计算

**第9轮** - Depths模块基础:
- `projects/scuba-physics/src/lib/depths/speeds.ts` (简单)
- `projects/scuba-physics/src/lib/depths/DepthLevels.ts` (中等)

**第10轮** - Depths模块核心:
- `projects/scuba-physics/src/lib/depths/Segments.ts` (中等)
- `projects/scuba-physics/src/lib/depths/PlanFactory.ts` (中等)

**第11轮** - Consumption模块基础:
- `projects/scuba-physics/src/lib/consumption/StandardTanks.ts` (简单)
- `projects/scuba-physics/src/lib/consumption/Diver.ts` (简单)
- `projects/scuba-physics/src/lib/consumption/Tanks.ts` (中等)

**第12轮** - Consumption模块中等算法:
- `projects/scuba-physics/src/lib/consumption/consumptionByMix.ts` (中等)

**第13轮** - Consumption模块复杂算法:
- `projects/scuba-physics/src/lib/consumption/consumption.ts` (复杂)

#### 第四阶段: 计算器模块 (第14-18轮)
**目标**: 实现各种专用计算器

**第14轮** - Calculators模块简单文件批量:
- `projects/scuba-physics/src/lib/calculators/altitudeCalculator.ts` (简单)
- `projects/scuba-physics/src/lib/calculators/weight.ts` (简单)
- `projects/scuba-physics/src/lib/calculators/SacCalculator.ts` (简单)

**第15轮** - Calculators模块中等文件1:
- `projects/scuba-physics/src/lib/calculators/NitroxCalculator.ts` (中等)
- `projects/scuba-physics/src/lib/calculators/OtuCalculator.ts` (中等)

**第16轮** - Calculators模块中等文件2:
- `projects/scuba-physics/src/lib/calculators/cnsCalculator.ts` (中等)
- `projects/scuba-physics/src/lib/calculators/blendPricing.ts` (中等)

**第17轮** - Calculators模块中等复杂:
- `projects/scuba-physics/src/lib/calculators/gasBlender.ts` (中等)

**第18轮** - Calculators模块复杂算法:
- `projects/scuba-physics/src/lib/calculators/realGasBlender.ts` (复杂)

#### 第五阶段: 核心算法基础 (第19-21轮)
**目标**: 减压算法基础组件

**第19轮** - Algorithm模块简单文件批量:
- `projects/scuba-physics/src/lib/algorithm/DefaultValues.ts` (简单)
- `projects/scuba-physics/src/lib/algorithm/Options.ts` (简单)
- `projects/scuba-physics/src/lib/algorithm/GradientFactors.ts` (简单)

**第20轮** - Algorithm模块中等文件1:
- `projects/scuba-physics/src/lib/algorithm/Compartments.ts` (中等)
- `projects/scuba-physics/src/lib/algorithm/ProfileEvents.ts` (中等)

**第21轮** - Algorithm模块中等文件2:
- `projects/scuba-physics/src/lib/algorithm/BuhlmannAlgorithmParameters.ts` (中等)
- `projects/scuba-physics/src/lib/algorithm/CalculatedProfile.ts` (中等)

#### 第六阶段: 核心减压算法 (第22-26轮)
**目标**: 最复杂的Bühlmann减压算法实现

**第22轮** - Algorithm组织接口:
- `projects/scuba-physics/src/lib/algorithm/Tissues.api.ts` (复杂)

**第23轮** - Algorithm核心组织计算:
- `projects/scuba-physics/src/lib/algorithm/Tissues.ts` (复杂)

**第24轮** - Algorithm配置文件管理:
- `projects/scuba-physics/src/lib/algorithm/ProfileTissues.ts` (复杂)

**第25轮** - Algorithm状态管理:
- `projects/scuba-physics/src/lib/algorithm/AlgorithmContext.ts` (复杂)

**第26轮** - Algorithm核心实现:
- `projects/scuba-physics/src/lib/algorithm/BuhlmannAlgorithm.ts` (复杂)

### 模块质量评估时间点
- **第4轮后**: 调用Test Quality Reviewer评估Common模块
- **第8轮后**: 调用Test Quality Reviewer评估Physics和Gases模块
- **第11轮后**: 调用Test Quality Reviewer评估Depths模块  
- **第13轮后**: 调用Test Quality Reviewer评估Consumption模块
- **第18轮后**: 调用Test Quality Reviewer评估Calculators模块
- **第26轮后**: 调用Test Quality Reviewer评估Algorithm模块和系统集成

### 成功标准
- 所有46个源文件完整移植
- 所有测试用例通过精度验证
- 代码质量达到规定标准
- 性能不低于原TypeScript版本

---

## 风险管控

### 高风险区域
- 核心Bühlmann减压算法的精度敏感性
- 复杂搜索算法的状态管理
- 跨语言数值计算的一致性保证

### 风险应对
- **增量验证**: 每个函数移植后立即验证
- **专家评审**: 关键算法多轮专家审查
- **回归测试**: 完善的自动化测试体系
- **质量把关**: 严格的质量检查点设置

---

*本开发计划作为整体指导文档，具体实施细节由各专业子代理根据实际代码分析结果动态确定。*