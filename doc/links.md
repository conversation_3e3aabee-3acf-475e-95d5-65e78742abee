# Links

Following list of external sources was used to develop this application and can be used as reference and study material.

# Inspired by:

* <https://github.com/Subsurface-divelog>

## At time of development also other implementations are available:

* <https://github.com/igaponov/deco-model>
* <https://github.com/oliver<PERSON>hnstone/npm-buhlmann-ZH-L16>
* Gas mixture calculator: <https://github.com/eianlei/pydplan>
* Mobile first DiveProMe+ <https://vlasovalexey.github.io/DiveProMe/>

## Alternatives

* <https://diveplanner.online/planner>
* <https://play.google.com/store/apps/details?id=com.techdeco.mcp>
* <https://play.google.com/store/apps/details?id=com.hhssoftware.multideco>
* <https://play.google.com/store/apps/details?id=com.diveprome.avlasov.diveprome>

## Based on following references:

* <https://github.com/archisgore/online-deco-console>
* <https://github.com/nyxtom/dive>
* <http://www.lizardland.co.uk/DIYDeco.html>
* <https://www.frogkick.nl/files/understanding_m-values.pdf>
* <https://diverclub.ru/File/literatur/Deep_Stops.pdf>
* <https://www.researchgate.net/publication/350176530_Update_per_03_2021_on_Decompression-Calculations_for_Trimix_Dives_with_PC-_Software_Gradient_Factors_do_they_repair_defective_algorithms_or_do_they_repair_defective_implementations>
* <https://www.divetable.info/skripte/theory.pdf>
* <https://thetheoreticaldiver.org/wordpress//var/lib/wordpress/wp-content/uploads/2017/11/dekotheorie1.pdf>
* <https://njscuba.net/gear/trng_10_deco.php>
* <https://thetheoreticaldiver.org/wordpress/index.php/2019/01/18/ndl-and-gradient-factors/>
* <https://www.diverite.com/articles/gradient-factors/?fbclid=IwAR2PnZ6IUzTzYJM9GhDdeS8HdSiGpqUBRPanSvibybAakAQXDbq_DK57MnA>
* <http://www.alertdiver.com/altitude_and_decompression_sickness?fbclid=IwAR2iDWh0ZwvB_oRMmkqQnlYaY3lc1W5NrWTX3DQvDR6N8B3lHXbG9aNFiTA>
* <https://gue.com/blog/calculated-confusion-can-o2-get-you-high/?fbclid=IwAR0qxKp2Jt7rrN6YFA9g0QxZLtENi1TjGZQ8pkxSMY5q4VQuaBUZwn5-u0Q>
* <https://www.shearwater.com/wp-content/uploads/2012/08/Oxygen_Toxicity_Calculations.pdf>
* <https://onedrive.live.com/?authkey=%21ACenWvokBcikpo0&id=24444EC0EEA41A1B%2112595&cid=24444EC0EEA41A1B&parId=root&parQt=sharedby&parCid=8D71FF700D902B86&o=OneUp>
* <https://www.hhssoftware.com/v-planner/decomyths.html>

## Related links

* Add air breaks
  * <https://dan.org/alert-diver/article/understanding-oxygen-toxicity/>
  * Subsurface 12min/6 min. to back gas (first tank), not checking its content
  * <https://decodoppler.wordpress.com/2012/08/02/air-breaks-what-are-they-and-do-people-take-them-for-the-wrong-reason/>
  * <https://www.diverite.com/uncategorized/oxygen-toxicity-and-ccr-rebreather-diving/>
  * <https://www.diverite.com/articles/oxygen-toxicity-signs-and-symptoms/>
  * <https://www.diverite.com/uncategorized/oxygen-toxicity-and-ccr-rebreather-diving/>
  * Diverite and decodopler: 20min./5 min.
* Gas blender
  * https://github.com/atdotde/realblender
  * https://github.com/subsurface/subsurface/blob/master/core/gas-model.c
  * https://thetheoreticaldiver.org/wordpress/index.php/2021/11/16/blending-real-gases/
  * https://scubaboard.com/community/threads/real-gas-the-real-deal.624674/#post-9772444
  * https://blender-toolkit.t-provider.de/
* Thalmann algorithm for deeper depths (https://indepthmag.com/thalmann-algorithm/)
* Deco myths <https://hhssoftware.com/v-planner/decomyths.html>

## Rebreathers

* <http://www.therebreathersite.nl/01_Informative/MenuInformative/MenuInformative.htm>
* <https://advanceddivermagazine.com/articles/ccrcalculations/ccrcalculationstxt.html>
* <https://www.bsac.com/news-and-blog/in-the-mix-closed-circuit-rebreather-gas-planning-part-2/>
* <https://diyrebreathers.com/data/uploads/documents/a-learners-guide-to-rebreathers-by-richard-pyle.pdf>
* <https://www.apdiving.com/en/wp-content/uploads/Rebreather_Vision_-Instruction-Manual-Nov12.pdf>
* <http://www.therebreathersite.nl/01_Informative/OrificesinRebreathers/OrificesinRebreathers.html>
