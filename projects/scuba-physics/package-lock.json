{"name": "scuba-physics", "version": "0.1.37", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "scuba-physics", "version": "0.1.37", "dependencies": {"tslib": "^2.7.0"}, "peerDependencies": {"@angular/common": "^18.2.7", "@angular/core": "^18.2.7"}}, "node_modules/@angular/common": {"version": "18.2.13", "resolved": "https://registry.npmjs.org/@angular/common/-/common-18.2.13.tgz", "integrity": "sha512-4ZqrNp1PoZo7VNvW+sbSc2CB2axP1sCH2wXl8B0wdjsj8JY1hF1OhuugwhpAHtGxqewed2kCXayE+ZJqSTV4jw==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"@angular/core": "18.2.13", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/core": {"version": "18.2.13", "resolved": "https://registry.npmjs.org/@angular/core/-/core-18.2.13.tgz", "integrity": "sha512-8mbWHMgO95OuFV1Ejy4oKmbe9NOJ3WazQf/f7wks8Bck7pcihd0IKhlPBNjFllbF5o+04EYSwFhEtvEgjMDClA==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "peerDependencies": {"rxjs": "^6.5.3 || ^7.4.0", "zone.js": "~0.14.10"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "license": "Apache-2.0", "peer": true, "dependencies": {"tslib": "^2.1.0"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/zone.js": {"version": "0.14.10", "resolved": "https://registry.npmjs.org/zone.js/-/zone.js-0.14.10.tgz", "integrity": "sha512-YGAhaO7J5ywOXW6InXNlLmfU194F8lVgu7bRntUF3TiG8Y3nBK0x1UJJuHUP/e8IyihkjCYqhCScpSwnlaSRkQ==", "license": "MIT", "peer": true}}}