@import "@fortawesome/fontawesome-svg-core/styles.css";
@import "mdb-angular-ui-kit/assets/scss/mdb.scss";

.table-noheader > tbody > tr:first-child > td {
    border: none;
}

.card-minheight {
    min-height: 500px;
}

// Fix for mdb dropdown, since ti doesn't respect parent element
.dropdown-menu {
    position: relative !important;
}

.card-max-height-md {
    max-height: 500px;
    overflow-y: auto;
}

.cursor-pointer {
    cursor: pointer;
}
