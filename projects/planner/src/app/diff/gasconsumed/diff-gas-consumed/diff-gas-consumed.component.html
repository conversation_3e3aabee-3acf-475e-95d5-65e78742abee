<div class="card">
    <div class="card-header">
        <div class="float-start mt-1">
            <fa-icon [icon]="icon" class="me-3"></fa-icon>
            <span>Gas Consumed</span>
        </div>
    </div>

    <div class="card-body card-minheight card-max-height-md">
        <div class="pb-3">
            <ng-container *ngIf="profileDif.showConsumption">
                <app-diff-gas-consumed-tank-chart *ngFor="let gasDiff of gasesDifference" [gasDiff]="gasDiff">
                </app-diff-gas-consumed-tank-chart>
            </ng-container>
        </div>
    </div>
</div>
