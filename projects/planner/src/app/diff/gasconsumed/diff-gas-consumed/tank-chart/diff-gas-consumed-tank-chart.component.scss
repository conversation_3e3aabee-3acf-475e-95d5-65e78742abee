@import "node_modules/mdb-angular-ui-kit/assets/scss/free/_variables.scss";

.gas-bar {
    padding: 5px 8px;
    height: 4em;
    font-size: 1em;
    border: 1px solid #a1a1a1;
    border-radius: .5em;
    background: transparent;
    box-shadow: none;
    overflow: visible;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.rounded-progress {
    position: absolute;
    height: 100%;
    left: 50%;
}

.reserve-overlay {
    position: relative;
}

.reserve-value {
    position: absolute;
    bottom: -7.75em;
    right: 5px;
    text-align: right;
}

.reserve-bar {
    position: absolute;
    padding: 0px;
    height: 5.25em;
    top: 0;
    left: calc(50% - 2px);
    width: 3px;
    background: darkgrey;
    text-align: right;
    font-size: .75em;
    z-index: 101;
}

.combined_wrapper {
    position: relative;
    width: 100%;
    height: 1.5em;
    z-index: 100;
}

.flip {
    transform: scaleX(-1);
    * {
        transform: scaleX(-1);
    }
}

.legend-bars {
    width: 16px;
    height: 16px;
    align-content: center;
    display: flex;
    padding-right: .5em;
    padding-left: .5em;
}

.z-index-1 {
    z-index: 1;
}

.difference-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    strong {
        padding-left: .2em;
        padding-right: .2em;
    }
}
