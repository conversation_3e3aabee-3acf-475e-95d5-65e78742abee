<div class="card" [formGroup]="tanksForm">
    <app-card-header
        cardTitle="Tanks"
        helpDocument="tanks"
        [headerIcon]="icon">
    </app-card-header>

        <!-- Small devices -->
        <div class="card-body d-md-none d-block" formArrayName="boundTanks">
            <mdb-accordion [flush]="true">
                <mdb-accordion-item *ngFor="let bound of tanksGroup.controls; let i = index" [formGroupName]="i">
                    <ng-template mdbAccordionItemHeader>
                        <div class="row">
                            <div class="col col-auto d-flex align-items-center">
                                {{ tanks[i].label }}
                            </div>
                            <div class="col col-1 pe-1">
                                <button *ngIf="i !== 0" class="m-0 p-1 btn btn-secondary" type="button"
                                    (click)="removeTank(i)">
                                    <fa-icon [icon]="minusIcon" class="p-0 m-0 fa-xl"></fa-icon>
                                </button>
                            </div>
                        </div>
                    </ng-template>
                    <ng-template mdbAccordionItemBody>
                        <div class="row">
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2">
                                <label for="sizeItemB-{{ i }}" class="form-label">Size
                                    [{{units.volume}}]:</label>
                                <app-tank-size [sizeForm]="bound" id="sizeItemB-{{ i }}" (sizeChange)="tankChanged(i)"
                                    (applyTemplate)="assignTankTemplate(i, $event)"></app-tank-size>
                                <div class="text-danger" *ngIf="tankSizeInvalid(i)">Needs to be number
                                    {{ranges.tankSizeLabel}}
                                </div>
                            </div>
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2" *ngIf="units.imperialUnits">
                                <label for="workPressureItemB-{{ i }}" class="form-label">Work p.
                                    [{{units.pressure}}]:</label>
                                <input formControlName="tankWorkPressure" class="form-control" type="number"
                                    (input)="tankChanged(i)" required [min]="ranges.tankPressure[0]"
                                    [max]="ranges.tankPressure[1]" step="1" id="workPressureItemB-{{ i }}"
                                    [class.is-invalid]="workPressureInvalid(i)" />
                                <div class="text-danger" *ngIf="workPressureInvalid(i)">
                                    Needs to be number {{ranges.tankPressureLabel}}</div>
                            </div>
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2">
                                <label for="pressureItemB-{{ i }}" class="form-label">Pressure
                                    [{{units.pressure}}]:</label>
                                <input formControlName="tankStartPressure" class="form-control" type="number"
                                    (input)="tankChanged(i)" required [min]="ranges.tankPressure[0]"
                                    [max]="ranges.tankPressure[1]" step="1" id="pressureItemB-{{ i }}"
                                    [class.is-invalid]="startPressureInvalid(i)" />
                                <div class="text-danger" *ngIf="startPressureInvalid(i)">
                                    Needs to be number {{ranges.tankPressureLabel}}</div>
                            </div>
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2" *ngIf="units.imperialUnits"></div>
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2">
                                <app-oxygen-dropdown [tank]="tanks[i]" id="o2Item-{{ i }}" (gasChange)="tankChanged(i)"
                                    (standardGasApplied)="standardGasApplied(i)" [nitroxForm]="bound"
                                    controlName="tankO2" [showBestMix]="false"
                                    [showTrimixGases]="true"></app-oxygen-dropdown>
                            </div>
                            <div class="col col-12 col-sm-6 m-0 pb-0 px-1 mt-2">
                                <label for="heItemB-{{ i }}" class="form-label">He [%]:</label>
                                <input formControlName="tankHe" class="form-control" type="number"
                                    (input)="tankChanged(i)" [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1"
                                    required [class.is-invalid]="gasHeInvalid(i)" id="heItemB-{{ i }}" />
                                <div class="text-danger" *ngIf="gasHeInvalid(i)">Needs to be number
                                    {{ranges.tankHeLabel}}</div>
                            </div>
                        </div>
                    </ng-template>
                </mdb-accordion-item>
            </mdb-accordion>
            <div class="row justify-content-end pe-2 m-3">
                <div class="col col-1">
                    <button class="p-1 btn btn-secondary" type="button" (click)="addTank()">
                        <fa-icon [icon]="plusIcon" class="fa-xl"></fa-icon>
                    </button>
                </div>
            </div>
        </div>

        <!-- Large devices -->
        <div class="card-body d-none d-md-block" formArrayName="boundTanks">
            <div class="row pb-2">
                <div class="col col-3 m-0 px-1">
                    <div class="row">
                        <div class="col col-1 p-0 m-0">
                        </div>
                        <div class="col ps-0">
                            Size [{{units.volume}}]
                        </div>
                    </div>
                </div>
                <div class="col col-2 m-0 px-1" *ngIf="units.imperialUnits">Work p.
                    [{{units.pressure}}]</div>
                <div class="col col-2 m-0 px-1">Pressure [{{units.pressure}}]</div>
                <div class="col col-2 m-0 px-1" [class.col-4]="!units.imperialUnits">O2 [%]</div>
                <div class="col col-2 m-0 px-1">He [%]</div>
                <div class="col col-1 m-0 px-1">
                    <button class="p-1 btn btn-secondary" type="button" (click)="addTank()">
                        <fa-icon [icon]="plusIcon" class="fa-xl"></fa-icon>
                    </button>
                </div>
            </div>

            <div *ngFor="let bound of tanksGroup.controls; let i = index" [formGroupName]="i" class="pb-2">
                <div class="row p-0">
                    <div class="col col-3 m-0 pb-0 px-1">
                        <div class="row">
                            <div class="col col-1 p-0 m-0">
                                {{ tanks[i].id }}.
                            </div>
                            <div class="col ps-0">
                                <app-tank-size [sizeForm]="bound" id="sizeItemB-{{ i }}" (sizeChange)="tankChanged(i)"
                                    (applyTemplate)="assignTankTemplate(i, $event)"></app-tank-size>
                            </div>
                        </div>
                    </div>
                    <div class="col col-2 m-0 pb-0 px-1" *ngIf="units.imperialUnits">
                        <input formControlName="tankWorkPressure" class="form-control" type="number"
                            (input)="tankChanged(i)" required [min]="ranges.tankPressure[0]"
                            [max]="ranges.tankPressure[1]" step="1" id="workPressureItem-{{ i }}"
                            [class.is-invalid]="workPressureInvalid(i)" />
                    </div>
                    <div class="col col-2 m-0 pb-0 px-1">
                        <input formControlName="tankStartPressure" class="form-control" type="number"
                            (input)="tankChanged(i)" required [min]="ranges.tankPressure[0]"
                            [max]="ranges.tankPressure[1]" step="1" id="pressureItem-{{ i }}"
                            [class.is-invalid]="startPressureInvalid(i)" />
                    </div>
                    <div class="col col-2 m-0 pb-0 px-1" [class.col-4]="!units.imperialUnits">
                        <app-oxygen-dropdown [tank]="tanks[i]" id="o2Item-{{ i }}" (gasChange)="tankChanged(i)"
                            (standardGasApplied)="standardGasApplied(i)" [nitroxForm]="bound" controlName="tankO2"
                            [showBestMix]="false" [showTitle]="false" [showTrimixGases]="true"></app-oxygen-dropdown>
                    </div>
                    <div class="col col-2 m-0 pb-0 px-1">
                        <input formControlName="tankHe" class="form-control" type="number" (input)="tankChanged(i)"
                            [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1" required
                            [class.is-invalid]="gasHeInvalid(i)" id="heItem-{{ i }}" />
                    </div>
                    <div class="col col-1 m-0 pb-0 px-1 align-self-center">
                        <button *ngIf="i !== 0" class="m-0 p-1 btn btn-secondary" type="button" (click)="removeTank(i)"
                            id="removeTank-{{ i }}">
                            <fa-icon [icon]="minusIcon" class="p-0 m-0 fa-xl"></fa-icon>
                        </button>
                    </div>
                </div>
                <div class="row p-0">
                    <div class="col col-11">
                        <div class="row">
                            <div class="col col-1">
                            </div>
                            <div class="col m-0 px-1">
                                <small class="form-text text-muted">SAC: {{gasSac(i) | number:'1.0-2'}}
                                    {{units.sac}}</small>
                                <app-gaslabel [tank]="tanks[i].tank" [toxicity]="toxicity" class="float-end">
                                </app-gaslabel>
                            </div>
                        </div>
                    </div>
                    <div class="col col-1"></div>
                </div>
                <div class="row p-0">
                    <div class="col col-11">
                        <div class="row">
                            <div class="col col-1">
                            </div>
                            <div class="col m-0 px-0">
                                <small class="text-danger" *ngIf="tankSizeInvalid(i)">Size needs to be number
                                    {{ranges.tankSizeLabel}}. </small>
                                <small class="text-danger" *ngIf="workPressureInvalid(i)">
                                    Working pressure needs to be number {{ranges.tankPressureLabel}}. </small>
                                <small class="text-danger" *ngIf="startPressureInvalid(i)">
                                    Start pressure needs to be number {{ranges.tankPressureLabel}}. </small>
                                <small class="text-danger" *ngIf="gasO2Invalid(i)">
                                    O2 needs to be number {{ranges.trimixOxygenLabel}}. </small>
                                <small class="text-danger" *ngIf="gasHeInvalid(i)">
                                    He needs to be number {{ranges.tankHeLabel}}.</small>
                            </div>
                        </div>
                    </div>
                    <div class="col col-1"></div>
                </div>
            </div>
        </div>
</div>
