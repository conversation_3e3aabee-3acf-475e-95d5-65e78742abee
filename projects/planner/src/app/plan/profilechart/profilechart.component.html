<div class="card">
    <app-card-header
        cardTitle="Dive profile"
        helpDocument="profile_chart"
        [headerIcon]="profileIcon">
        <button class="btn btn-sm btn-secondary ms-2" [ngClass]="{ 'active': showHeatMap }" type="button"
                data-toggle="button" [attr.aria-pressed]="showHeatMap" title="Show tissues heatmap"
                autocomplete="off" (click)="switchHeatMap()">
            <fa-icon [icon]="heatmapIcon" class="fa-lg"></fa-icon>
        </button>
        <button class="btn btn-sm btn-secondary ms-2" [ngClass]="{ 'active': showEmergencyAscent }" type="button"
                data-toggle="button" [attr.aria-pressed]="showEmergencyAscent" title="Show emergency ascent"
                autocomplete="off" (click)="switchEmergencyAscent()">
            <fa-icon [icon]="emergencyIcon" class="fa-lg"></fa-icon>
        </button>
    </app-card-header>
    <div class="card-body card-minheight">
        <app-calculating [show]="!infoCalculated"></app-calculating>
        <div id="diveplot" class="dive-plot" [ngClass]="{
              'plot-display': profileCalculated,
              'plot-hidden': !profileCalculated
            }">
        </div>
        <div id="heatmapplot" class="heatmap-plot" [ngClass]="{
            'plot-display': profileCalculated,
            'plot-hidden': !profileCalculated || !showHeatMap
          }">
        </div>
    </div>
</div>
