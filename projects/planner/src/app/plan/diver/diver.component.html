<div class="mt-3 row">
    <ng-container [formGroup]="diverForm">
        <div class="pt-2 mt-2 mb-4 col col-12 col-md-6">
            <label for="rmv" class="form-label" mdbLabel>RMV [{{units.rmv}}]:</label>
            <input class="form-control" id="rmv" formControlName="rmv" type="number" (input)="inputChanged()"
                [min]="ranges.diverRmv[0]" [max]="ranges.diverRmv[1]" [step]="rmvStep"
                [class.is-invalid]="rmvInvalid" />
            <div class="invalid-feedback position-absolute">Needs to be number {{ranges.diverRmvLabel}}</div>
        </div>
        <div class="pt-2 mt-2 mb-4 col col-12 col-md-6">
            <label for="stressRmv" class="form-label" mdbLabel>Stress RMV [{{units.rmv}}]:</label>
            <input class="form-control" id="stressRmv" formControlName="stressRmv" type="number" (input)="inputChanged()"
                   [min]="ranges.diverRmv[0]" [max]="ranges.diverRmv[1]" [step]="rmvStep"
                   [class.is-invalid]="stressRmvInvalid" />
            <div class="invalid-feedback position-absolute">Needs to be number {{ranges.diverRmvLabel}}</div>
        </div>
        <div class="pt-2 mt-2 mb-4 col col-12 col-md-6">
            <app-pp-o2 [maxPpO2]="diver.maxPpO2" (ppO2Change)="maxPpO2Changed($event)" label="Maximum ppO2:"
                [pO2Form]="diverForm" controlName="maxPO2"></app-pp-o2>
        </div>
        <div class="pt-2 mt-2 mb-4 col col-12 col-md-6">
            <app-pp-o2 [maxPpO2]="diver.maxDecoPpO2" (ppO2Change)="maxDecoPpO2Changed($event)"
                label="Maximum deco ppO2:" [pO2Form]="diverForm" controlName="maxDecoPO2"></app-pp-o2>
        </div>
    </ng-container>
</div>
