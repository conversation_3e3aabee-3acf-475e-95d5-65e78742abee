<ng-container [formGroup]="depthForm">
    <label for="depthField" class="form-label" mdbLabel>Depth [{{units.length}}]:</label>
    <input id="depthField" [formControlName]="controlName" class="form-control" type="number" required
        [min]="units.ranges.depth[0]" [max]="units.ranges.depth[1]" step="1" (input)="depthChanged()"
        [class.is-invalid]="depthInvalid" />
    <div class="invalid-feedback position-absolute">Needs to be number {{units.ranges.depthLabel}}</div>
    <div class="text-muted form-text" [class.invisible]="depthInvalid">Best mix is {{ bestNitroxMix }}</div>
    <button type="button" class="btn btn-secondary mt-2" id="applyMaxDepthBtn"
            (click)="applyMaxDepth()" title="Set maximum depth by gas narcotic limit">Max</button>
</ng-container>
