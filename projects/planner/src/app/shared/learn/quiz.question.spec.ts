import { Question } from './quiz.question';
import { QuestionTemplate, RoundType, NumberVariable } from "./learn.models";

describe('Quiz Question', () => {
    it('Renders variables into question template', () => {
        const questionTemplate = new QuestionTemplate("Question: with {cccc} and {bbbb} and {aaaa}.",
            1,
            RoundType.ceil,
            [
                new NumberVariable('aaaa', 1, 1),
                new NumberVariable('bbbb', 2, 2),
                new NumberVariable('cccc', 3, 3),
            ],
            () => 0);

        const question = new Question(questionTemplate);

        expect(question.renderedQuestion).toEqual("Question: with 3 and 2 and 1.");
    });

    const createQuestionWithRounding = (roundType: RoundType) => {
        const template = new QuestionTemplate("Question: with {aaaa}.",
            1,
            roundType,
            [],
            (_) => 1.25);
        const question = new Question(template);
        question.userAnswer = "1";
        return question;
    };

    it('Rounds DOWN the question correct answer', () => {
        const question = createQuestionWithRounding(RoundType.floor);
        question.validateAnswer();

        expect(question.correctAnswer).toBeCloseTo(1.2, 1);
    });

    it('Rounds UP the question correct answer', () => {
        const question = createQuestionWithRounding(RoundType.ceil);
        question.validateAnswer();

        expect(question.correctAnswer).toBeCloseTo(1.3, 1);
    });

    it('Does create correct answer, even user answer was empty', () => {
        const question = createQuestionWithRounding(RoundType.ceil);
        question.userAnswer = '';
        question.validateAnswer();

        expect(question.correctAnswer).toBeCloseTo(1.3, 1);
    });
});
