<div class="row mt-5">
    <form [formGroup]="settingsForm" class="row">
        <div class="col col-12 col-sm-7 col-lg-5 col-xl-4 col-xxl-3 mt-3">
            <div class="card">
                <app-card-header
                    cardTitle="Units"
                    [headerIcon]="flagIcon"
                    helpDocument="settings">
                </app-card-header>
                <div class="card-body">
                    <div class="form-check">
                        <input type="radio" id="metricRadio" class="form-check-input" formControlName="imperialUnits"
                               [value]="false" />
                        <label class="custom-control-label" for="metricRadio" value="metric">Metric units (m, l,
                            bar)</label>
                    </div>
                    <div class="form-check">
                        <input type="radio" id="imperialRadio" class="form-check-input" formControlName="imperialUnits"
                               [value]="true" />
                        <label class="custom-control-label" for="imperialRadio">Imperial units (ft, cuft, psi)<br>
                            (Experimental, not recommended)</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="col col-12 col-sm-7 col-lg-5 col-xl-4 col-xxl-3 mt-3">
            <div class="card">
                <app-card-header
                    cardTitle="Limits"
                    [headerIcon]="diverIcon"
                    helpDocument="settings">
                </app-card-header>
                <div class="card-body">
                    <div class="mb-4 col col-12 col-md-6 col-lg-12 col-xl-6">
                        <label for="maxDensity" class="form-label" mdbLabel>Max gas density [{{units.density}}]:
                        </label>
                        <input class="form-control" type="number" id="maxDensity"
                               formControlName="maxDensity" required [min]="ranges.maxDensity[0]"
                               [max]="ranges.maxDensity[1]" [step]="densityStep" [class.is-invalid]="maxDensityInvalid" />
                        <div class="invalid-feedback position-absolute">Needs to be number {{ranges.maxDensityLabel}}</div>
                    </div>
                    <div class="mb-4 col col-12 col-md-6 col-lg-12 col-xl-6">
                        <label for="primaryTankReserve" class="form-label" mdbLabel>Minimum Primary tank reserve [{{units.pressure}}]:
                        </label>
                        <input class="form-control" type="number" id="primaryTankReserve"
                               formControlName="primaryTankReserve" required [min]="ranges.tankPressure[0]"
                               [max]="ranges.tankPressure[1]" step="1" [class.is-invalid]="primaryTankReserveInvalid" />
                        <div class="invalid-feedback position-absolute">Needs to be number {{ranges.tankPressureLabel}}</div>
                    </div>
                    <div class="mb-4 col col-12 col-md-6 col-lg-12 col-xl-6">
                        <label for="stageTankReserve" class="form-label" mdbLabel>Minimum Stage tank reserve [{{units.pressure}}]:
                        </label>
                        <input class="form-control" type="number" id="stageTankReserve"
                               formControlName="stageTankReserve" required [min]="ranges.tankPressure[0]"
                               [max]="ranges.tankPressure[1]" step="1" [class.is-invalid]="stageTankReserveInvalid" />
                        <div class="invalid-feedback position-absolute">Needs to be number {{ranges.tankPressureLabel}}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col col-12 col-sm-7 col-lg-5 col-xl-4 col-xxl-3 mt-3">
            <div class="card">
                <app-card-header
                    cardTitle="Ignored Issues (warnings/errors)"
                    [headerIcon]="diverIcon"
                    helpDocument="settings">
                </app-card-header>
                <div class="card-body">
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="icdIgnored" formControlName="icdIgnored" mdbCheckbox />
                        <label class="custom-control-label" for="icdIgnored">Isobaric counter diffusion (ICD)</label>
                    </div>
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="densityIgnored" formControlName="densityIgnored" mdbCheckbox />
                        <label class="custom-control-label" for="densityIgnored">Maximum gas density Exceeded</label>
                    </div>
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="noDecoIgnored" formControlName="noDecoIgnored" mdbCheckbox />
                        <label class="custom-control-label" for="noDecoIgnored">No decompression limit reached</label>
                    </div>
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="missingAirBreak" formControlName="missingAirBreak" mdbCheckbox />
                        <label class="custom-control-label" for="missingAirBreak">Missing air break</label>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="form-group mt-3">
        <button type="button" class="btn btn-primary me-2" (click)="location.back()">Back</button>
        <button type="button" class="btn btn-secondary me-2" (click)="resetToDefault()" >Reset to Default</button>
        <button type="button" class="btn btn-secondary" (click)="use()" [hidden]="!settingsForm.valid">Use</button>
    </div>
</div>
