<div class="row mt-5">
    <div class="col col-sm-8 col-md-6 col-lg-5 col-xl-4 col-xxl-3 mt-3">
        <div class="card">
            <app-card-header
                cardTitle="Altitude calculator"
                [headerIcon]="calcIcon"
                helpDocument="altitude">
            </app-card-header>
            <div class="card-body">
                <form [formGroup]="altitudeForm">
                    <div>
                        <div class="pt-2 mb-4">
                            <label for="pressure" class="form-label" mdbLabel>Atmospheric pressure
                                [{{units.pressure}}]:</label>
                            <input class="form-control" name="pressure" id="pressure" type="number"
                                formControlName="pressure" (input)="pressureChanged()" [min]="ranges.altitudePressure[0]" [max]="ranges.altitudePressure[1]"
                                step="0.01" [class.is-invalid]="pressureInvalid"
                                [value]="calcPressure | number: '1.0-6'" />
                            <div class="invalid-feedback position-absolute">Needs to be number {{ranges.altitudePressure[0]}} - {{ranges.altitudePressure[1]}} {{units.pressure}}
                            </div>
                        </div>
                    </div>
                    <div>
                        <app-altitude [altitude]="calc.altitude" [altitudeForm]="altitudeForm"
                            (inputChange)="altitudeChanged($event)" />
                    </div>
                    <div class="pt-2 mb-4">
                        <label for="actualDepth" class="form-label" mdbLabel>Actual depth [{{units.length}}]:</label>
                        <input class="form-control" name="actualDepth" id="actualDepth" type="number"
                            formControlName="actualDepth" (input)="inputChanged()" [min]="ranges.depth[0]"
                            [max]="ranges.depth[1]" step="1" [class.is-invalid]="actualDepthInvalid" />
                    </div>

                    <div class="pt-2 mb-4 form-outline">
                        <label for="actualDepth" class="form-label" mdbLabel>Theoretical sea level depth
                            [{{units.length}}]:</label>
                        <input class="form-control" name="theoreticalDepth" id="theoreticalDepth" type="number"
                            [value]="theoreticalDepth | number: '1.0-2'" readonly />
                    </div>
                </form>
            </div>
        </div>
        <div class="form-group mt-3">
            <button type="button" class="btn btn-primary me-2" (click)="location.back()">Back</button>
        </div>
    </div>
</div>
