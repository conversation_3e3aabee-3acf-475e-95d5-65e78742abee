<div class="row mt-5">
    <div class="col col-12 col-md-7 col-lg-4 col-xl-5 col-xxl-4 mt-3">
        <div class="card">
            <app-card-header
                cardTitle="Gas mixture"
                helpDocument="gas_properties"
                [headerIcon]="calcIcon">
            </app-card-header>
            <div class="card-body">
                <form [formGroup]="gasForm">
                    <div class="row">
                        <div class="col-12 col-sm-6 col-lg-12 col-xl-6">
                            <div class="pt-2 mb-4">
                                <app-oxygen-dropdown [tank]="tank" id="o2Item" (gasChange)="inputChanged()"
                                                     (standardGasApplied)="standardGasApplied()" [nitroxForm]="gasForm" controlName="o2"
                                                     [showBestMix]="false" [showTrimixGases]="true"></app-oxygen-dropdown>
                                <small class="text-muted">Nitrox {{ nitrogen | number: '1.0-2' }} [%]</small>
                            </div>
                            <div class="pt-2 mb-4">
                                <label for="heItem" class="form-label">He [%]:</label>
                                <input formControlName="he" class="form-control" type="number" (input)="inputChanged()"
                                       [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1" required
                                       [class.is-invalid]="gasHeInvalid" id="heItem" />
                                <div class="text-danger" *ngIf="gasHeInvalid">Needs to be number
                                    {{ranges.tankHeLabel}}</div>
                            </div>
                            <div class="pt-2 mb-4">
                                <app-pp-o2 [maxPpO2]="calc.maxPpO2" (ppO2Change)="inputChanged()" label="Maximum ppO2:"
                                           [pO2Form]="gasForm" controlName="maxPO2"></app-pp-o2>
                            </div>
                        </div>
                        <div class="col-12 col-sm-6 col-lg-12 col-xl-6">
                            <div class="pt-2 mb-4">
                                <label for="maxNarcoticDepth" class="form-label" mdbLabel>Max narcotic depth
                                    [{{units.length}}]:</label>
                                <input id="maxNarcoticDepth" class="form-control" type="number" formControlName="mndLimit"
                                       required [min]="ranges.narcoticDepth[0]" [max]="ranges.narcoticDepth[1]" step="1"
                                       (input)="inputChanged()" [class.is-invalid]="narcoticDepthInvalid" />
                                <div class="invalid-feedback position-absolute">
                                    Needs to be number {{ranges.narcoticDepthLabel}}
                                </div>
                            </div>
                            <div class="pt-2 mb-4">
                                <div class="form-check form-switch pb-1">
                                    <input type="checkbox" class="form-check-input" id="oxygenNarcotic" mdbCheckbox
                                           [checked]="calc.oxygenNarcotic" (change)="switchOxygenNarcotic()" />
                                    <label class="form-check-label" for="oxygenNarcotic">Oxygen is narcotic</label>
                                </div>
                            </div>
                            <div class="pt-2 mb-4">
                                <label for="depth" class="form-label" mdbLabel>Properties at depth [{{units.length}}]:
                                </label>
                                <input class="form-control" type="number" (input)="inputChanged()" id="depth"
                                       formControlName="depth" required [min]="ranges.depth[0]" [max]="ranges.depth[1]" step="1"
                                       [class.is-invalid]="depthInvalid" />
                                <div class="invalid-feedback position-absolute">Needs to be number
                                    {{ranges.depth}}</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col col-12 col-md-5 col-lg-4 col-xl-4 col-xxl-3 mt-3">
        <div class="card">
            <div class="card-header">
                <span>Gas properties</span>
            </div>
            <div class="card-body">
                <table class="table table-sm p-0">
                    <tbody>
                        <tr>
                            <td>Oxygen partial pressure</td>
                            <td class="table-active">{{ calc.ppO2 | number: '1.0-2' }}</td>
                        </tr>
                        <tr>
                            <td>Helium partial pressure</td>
                            <td class="table-active">{{ calc.ppHe | number: '1.0-2'}}</td>
                        </tr>
                        <tr>
                            <td>Nitrogen partial pressure</td>
                            <td class="table-active">{{ calc.ppN2 | number: '1.0-2' }}</td>
                        </tr>
                        <tr>
                            <td>Total partial pressure</td>
                            <td class="table-active">{{ calc.totalPp | number: '1.0-2' }}</td>
                        </tr>
                        <tr>
                            <td>Minimum depth [{{units.length}}]
                                <fa-icon *ngIf="calc.minPpO2Exceeded" [icon]="warningIcon" class="me-2 text-warning"></fa-icon>
                            </td>
                            <td class="table-active">{{ calc.minDepth | number: '1.0-1' }}</td>
                        </tr>
                        <tr>
                            <td>Maximum depth [{{units.length}}]
                                <fa-icon *ngIf="calc.maxPpO2Exceeded" [icon]="warningIcon" class="me-2 text-warning"></fa-icon>
                            </td>
                            <td class="table-active">{{ calc.maxDepth | number: '1.0-1' }}</td>
                        </tr>
                        <tr *ngIf="isNitrox">
                            <td>Equivalent air depth [{{units.length}}]</td>
                            <td class="table-active">{{ calc.ead | number: '1.0-1' }}</td>
                        </tr>
                        <tr>
                            <td>Equivalent narcotic depth [{{units.length}}]
                                <fa-icon *ngIf="calc.mndExceeded" [icon]="warningIcon" class="me-2 text-warning"></fa-icon>
                            </td>
                            <td class="table-active">{{ calc.end | number: '1.0-1' }}</td>
                        </tr>
                        <tr>
                            <td>Maximum narcotic depth [{{units.length}}]</td>
                            <td class="table-active">{{ calc.mnd | number: '1.0-1' }}</td>
                        </tr>
                        <tr>
                            <td>Density [{{units.density}}]
                                <fa-icon *ngIf="calc.densityExceeded" [icon]="warningIcon" class="me-2 text-warning"></fa-icon></td>
                            <td class="table-active">{{ calc.density | number: '1.0-2' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col col-12 col-md-12 col-lg-4 col-xl-3 col-xxl-5 mt-3">
        <div class="card">
            <div class="card-header">
                <fa-icon [icon]="warningIcon" class="me-3"></fa-icon>
                <span>Warnings</span>
            </div>
            <div class="card-body">
                <div class="mb-4">{{ depthConverterWarning }}</div>
                <div *ngIf="calc.minPpO2Exceeded" class="alert alert-warning">
                    <fa-icon [icon]="warningIcon" class="me-2"></fa-icon>
                    <strong>Minimum depth: </strong>
                    <span>Exceeded minimum value of Oxygen partial pressure ({{calc.minPpO2}}), which defines minimum depth.
                        Add more oxygen content or reduce required depth.</span>
                </div>
                <div *ngIf="calc.maxPpO2Exceeded" class="alert alert-warning">
                    <fa-icon [icon]="warningIcon" class="me-2"></fa-icon>
                    <strong>Maximum depth: </strong>
                    <span>Exceeded maximum value of Oxygen partial pressure ({{calc.maxPpO2}}). Increase the maximum ppO2,
                        reduce oxygen content or reduce required depth.</span>
                </div>
                <div *ngIf="calc.mndExceeded" class="alert alert-warning">
                    <fa-icon [icon]="warningIcon" class="me-2"></fa-icon>
                    <strong>Equivalent narcotic depth: </strong>
                    <span>Exceeded maximum value of narcotic depth ({{calc.mndLimit}} {{units.length}})
                        controlled by partial pressure of toxic parts (oxygen and nitrogen).
                        Reduce nitrogen content by adding helium, adding oxygen or reduce required depth.</span>
                </div>
                <div *ngIf="calc.densityExceeded" class="alert alert-warning">
                    <fa-icon [icon]="warningIcon" class="me-2"></fa-icon>
                    <strong>Maximum gas density: </strong>
                    <span>Exceeded maximum value of gas density ({{calc.maxDensity}} {{units.density}}). Reduce oxygen content or add more helium.</span>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="form-group mt-3">
        <button type="button" class="btn btn-primary me-2" (click)="location.back()">Back</button>
    </div>
</div>
