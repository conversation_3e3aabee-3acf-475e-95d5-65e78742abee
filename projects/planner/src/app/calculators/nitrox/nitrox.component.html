<div class="row mt-5">
    <div class="col col-sm-8 col-md-6 col-lg-5 col-xl-4 col-xxl-3 mt-3">
        <div class="card">
            <app-card-header
            cardTitle="Nitrox calculator"
            helpDocument="nitrox"
            [headerIcon]="calcIcon">
            </app-card-header>
            <div class="card-body">
                <form [formGroup]="nitroxForm">
                    <div class="mb-4">{{ depthConverterWarning }}</div>
                    <div class="btn-group mb-3" role="group">
                        <button type="button" class="btn" [class.btn-primary]="calc.inMod"
                            (click)="toMod()">MOD</button>
                        <button type="button" class="btn" [class.btn-primary]="calc.inBestMix"
                            (click)="toBestMix()">Best mix</button>
                        <button type="button" class="btn" [class.btn-primary]="calc.inPO2"
                            (click)="toPO2()">ppO2</button>
                        <button type="button" class="btn" [class.btn-primary]="calc.inEad"
                                (click)="toEad()">EAD</button>
                    </div>
                    <ng-container *ngIf="!calc.inPO2 && !calc.inEad">
                        <div class="pt-2 mb-4">
                            <label for="pO2" class="form-label" mdbLabel>ppO2:</label>
                            <input class="form-control" name="pO2" id="pO2" type="number" formControlName="pO2"
                                (input)="inputChanged()" [min]="ranges.ppO2[0]" [max]="ranges.ppO2[1]" step="0.1"
                                [class.is-invalid]="pO2Invalid" />
                            <div class="invalid-feedback position-absolute">Needs to be number 0.21-3
                            </div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="!calc.inBestMix">
                        <div class="pt-2 mb-4">
                            <app-oxygen-dropdown [showBestMix]="false" (gasChange)="inputChanged()"
                                (standardGasApplied)="inputChanged()" [nitroxForm]="nitroxForm"
                                controlName="fO2"></app-oxygen-dropdown>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="!calc.inMod && !calc.inEad">
                        <div class="pt-2 mb-4">
                            <label for="mod" class="form-label" mdbLabel>MOD [{{units.length}}]:</label>
                            <input class="form-control" name="mod" id="mod" type="number" formControlName="mod"
                                (input)="inputChanged()" [min]="ranges.depth[0]" [max]="ranges.depth[1]" step="1"
                                aria-describedby="eadDescribe" [class.is-invalid]="modInvalid" />
                            <div class="invalid-feedback position-absolute">Needs to
                                be number {{ranges.depthLabel}}</div>
                            <div id="eadDescribe" class="form-text" [class.invisible]="modInvalid">
                                EAD: {{ ead | number: '1.0-1' }} {{units.length}}</div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="calc.inMod">
                        <div class="pt-2 mb-4 form-outline" data-mdb-input-init>
                            <label for="modr" class="form-label" mdbLabel>MOD [{{units.length}}]:</label>
                            <input class="form-control" [value]="calcMod | number: '1.0-1'"
                                id="modr" readonly/>
                            <small class="form-text text-muted">EAD: {{ ead | number: '1.0-1' }}
                                {{units.length}}</small>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="calc.inEad">
                        <div class="pt-2 mb-4">
                            <label for="depth" class="form-label" mdbLabel>Depth [{{units.length}}]:</label>
                            <input class="form-control" name="depth" id="depth" type="number" formControlName="depth"
                                   (input)="inputChanged()" [min]="ranges.depth[0]" [max]="ranges.depth[1]" step="1"
                                   [class.is-invalid]="depthInvalid" />
                            <div class="invalid-feedback position-absolute">Needs to
                                be number {{ranges.depthLabel}}</div>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="calc.inBestMix">
                        <div class="pt-2 mb-4 form-outline">
                            <label for="fO2r" class="form-label" mdbLabel>O2 [%]:</label>
                            <input class="form-control" [value]="calc.fO2 | number: '1.0-1'"
                                id="fO2r" readonly/>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="calc.inPO2">
                        <div class="pt-2 mb-4 form-outline">
                            <label for="pO2r" class="form-label" mdbLabel>ppO2:</label>
                            <input class="form-control" [value]="calc.pO2 | number: '1.0-2'"
                                id="pO2r" readonly/>
                        </div>
                    </ng-container>
                    <ng-container *ngIf="calc.inEad">
                        <div class="pt-2 mb-4 form-outline">
                            <label for="eadr" class="form-label" mdbLabel>EAD [{{units.length}}]:</label>
                            <input class="form-control" [value]="eadAtDepth | number: '1.0-2'"
                                   id="eadr" readonly/>
                        </div>
                    </ng-container>
                    <div class="pt-2 pb-4 pe-3 mb-4">
                        <ng-container *ngIf="nitroxForm.errors?.lowMod">
                            <div class="text-danger form-text position-absolute">
                                Too small final operation depth. Increase ppO2 or reduce Percent O2.
                            </div>
                        </ng-container>
                    </div>
                </form>
            </div>
        </div>
        <div class="form-group mt-3">
            <button type="button" class="btn btn-primary me-2" (click)="location.back()">Back</button>
            <button type="button" class="btn btn-secondary" (click)="use()" *ngIf="!nitroxForm.invalid">Use</button>
        </div>
    </div>
</div>
