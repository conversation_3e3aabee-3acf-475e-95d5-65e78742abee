<form [formGroup]="blenderForm">
    <div class="row mt-5">
        <div class="col col-12 col-md-8 col-lg-6 col-xxl-5 mt-3">
            <div class="card">
                <app-card-header
                    cardTitle="Gas blender"
                    [headerIcon]="calcIcon"
                    helpDocument="gas_blender"
                >
                    <button class="btn btn-sm btn-secondary ms-2" [ngClass]="{ 'active': togglePricing }" type="button"
                            id="pricingToggle" data-toggle="button" title="Show pricing"
                            [attr.aria-pressed]="togglePricing" autocomplete="off" (click)="togglePricing()">
                        <fa-icon [icon]="dollarIcon" class="fa-lg"></fa-icon>
                    </button>
                </app-card-header>
                <div class="card-body row">
                    <div class="col col-12 col-sm-4 mb-2">
                        <div>Source</div>
                        <div class="mb-2">
                            <app-oxygen-dropdown [tank]="calc.sourceTank" (gasChange)="applyChange()"
                                                 [showBestMix]="false" (standardGasApplied)="applyTemplate()" controlName="sourceO2"
                                                 [showTrimixGases]="true" [nitroxForm]="blenderForm" />
                        </div>
                        <div class="mb-2">
                            <label for="sourceHe" class="form-label">He [%]:</label>
                            <input formControlName="sourceHe" class="form-control" type="number"
                                   (input)="applyChange()" [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1"
                                   required [class.is-invalid]="sourceHeInvalid" id="sourceHe" />
                            <div class="text-danger" *ngIf="sourceHeInvalid">Needs to be number
                                {{ranges.tankHeLabel}}</div>
                        </div>
                        <div>
                            <label for="sourcePressure" class="form-label">Pressure
                                [{{units.pressure}}]:</label>
                            <input formControlName="sourcePressure" class="form-control" type="number"
                                   (input)="applyChange()" required [min]="0"
                                   [max]="ranges.tankPressure[1]" step="1" id="sourcePressure"
                                   [class.is-invalid]="sourcePressureInvalid" />
                            <div class="text-danger" *ngIf="sourcePressureInvalid">
                                Needs to be number {{ranges.tankPressureLabel}}</div>
                        </div>
                    </div>
                    <div class="col col-12 col-sm-4 mb-2">
                        <div>Top mix</div>
                        <div class="mb-2">
                            <app-oxygen-dropdown [tank]="calc.topMix" (gasChange)="applyChange()"
                                                 [showBestMix]="false" (standardGasApplied)="applyTemplate()" controlName="topMixO2"
                                                 [showTrimixGases]="true" [nitroxForm]="blenderForm" />
                        </div>
                        <div class="mb-2">
                            <label for="topMixHe" class="form-label">He [%]:</label>
                            <input formControlName="topMixHe" class="form-control" type="number"
                                   (input)="applyChange()" [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1"
                                   required [class.is-invalid]="topMixHeInvalid" id="topMixHe" />
                            <div class="text-danger" *ngIf="topMixHeInvalid">Needs to be number
                                {{ranges.tankHeLabel}}</div>
                        </div>
                    </div>
                    <div class="col col-12 col-sm-4 mb-2">
                        <div>Target</div>
                        <div class="mb-2">
                            <app-oxygen-dropdown [tank]="calc.targetTank" (gasChange)="applyChange()"
                                                 [showBestMix]="false" (standardGasApplied)="applyTemplate()" controlName="targetO2"
                                                 [showTrimixGases]="true" [nitroxForm]="blenderForm" />
                        </div>
                        <div class="mb-2">
                            <label for="sourceHe" class="form-label">He [%]:</label>
                            <input formControlName="targetHe" class="form-control" type="number"
                                   (input)="applyChange()" [min]="ranges.tankHe[0]" [max]="ranges.tankHe[1]" step="1"
                                   required [class.is-invalid]="targetHeInvalid" id="targetHe" />
                            <div class="text-danger" *ngIf="targetHeInvalid">Needs to be number
                                {{ranges.tankHeLabel}}</div>
                        </div>
                        <div>
                            <label for="sourcePressure" class="form-label">Pressure
                                [{{units.pressure}}]:</label>
                            <input formControlName="targetPressure" class="form-control" type="number"
                                   (input)="applyChange()" required [min]="ranges.tankPressure[0]"
                                   [max]="ranges.tankPressure[1]" step="1" id="targetPressure"
                                   [class.is-invalid]="targetPressureInvalid" />
                            <div class="text-danger" *ngIf="targetPressureInvalid">
                                Needs to be number {{ranges.tankPressureLabel}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col col-12 col-md-4 col-lg-4 col-xxl-3 mt-3">
            <div class="card">
                <div class="card-header">
                    <span>Results</span>
                </div>
                <div class="card-body">
                    <table *ngIf="!calc.unableToCalculate" class="table table-sm" >
                        <tbody>
                            <tr *ngIf="calc.needsRemove">
                                <td>Remove from source [{{units.pressure}}]</td>
                                <td class="table-active">{{ calc.removeFromSource | number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td>Add O2 [{{units.pressure}}]</td>
                                <td class="table-active">{{ calc.addO2 | number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td>Add He [{{units.pressure}}]</td>
                                <td class="table-active">{{ calc.addHe | number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td>Add Top mix [{{units.pressure}}]</td>
                                <td class="table-active">{{ calc.addTop | number:'1.0-1' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div *ngIf="calc.unableToCalculate" class="alert alert-danger">
                        <fa-icon [icon]="exclamationIcon" class="me-2"></fa-icon>
                        <span>Unable to calculate the required mix, because the Top mix contains more oxygen or helium,
                            than target mix.</span>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="showPricing" class="col col-12 col-md-8 col-lg-6 col-xxl-5 mt-3">
            <div class="card">
                <div class="card-header">
                    <span>Unit prices</span>
                </div>
                <div class="card-body row">
                    <div class="col col-12 col-sm-4 mb-2">
                        <label for="o2UnitPrice" class="form-label">Unit price O2:</label>
                        <input formControlName="o2UnitPrice" class="form-control" type="number"
                               (input)="applyChange()" [min]="ranges.money[0]" [max]="ranges.money[1]" step="1"
                               required [class.is-invalid]="o2UnitPriceInvalid" id="o2UnitPrice"/>
                        <div class="text-danger" *ngIf="o2UnitPriceInvalid">Needs to be number
                            {{ranges.moneyLabel}}</div>
                    </div>
                    <div class="col col-12 col-sm-4 mb-2">
                        <label for="heUnitPrice" class="form-label">Unit price He:</label>
                        <input formControlName="heUnitPrice" class="form-control" type="number"
                               (input)="applyChange()" [min]="ranges.money[0]" [max]="ranges.money[1]"  step="1"
                               required [class.is-invalid]="heUnitPriceInvalid" id="heUnitPrice" />
                        <div class="text-danger" *ngIf="heUnitPriceInvalid">Needs to be number
                            {{ranges.moneyLabel}}</div>
                    </div>
                    <div class="col col-12 col-sm-4 mb-2">
                        <label for="topMixUnitPrice" class="form-label">Unit price Top mix:</label>
                        <input formControlName="topMixUnitPrice" class="form-control" type="number"
                               (input)="applyChange()" [min]="ranges.money[0]" [max]="ranges.money[1]"  step="1"
                               required [class.is-invalid]="topMixUnitPriceInvalid" id="topMixUnitPrice" />
                        <div class="text-danger" *ngIf="topMixUnitPriceInvalid">Needs to be number
                            {{ranges.moneyLabel}}</div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="showPricing" class="col col-12 col-md-4 col-lg-4 col-xxl-3 mt-3">
            <div class="card">
                <div class="card-header">
                    <span>Mix price</span>
                </div>
                <div class="card-body">
                    <table *ngIf="!calc.unableToCalculate" class="table table-sm" >
                        <tbody>
                            <tr>
                                <td>O2 price</td>
                                <td class="table-active">{{ pricing.o2Price | number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td>He price</td>
                                <td class="table-active">{{ pricing.hePrice | number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td>Top mix price</td>
                                <td class="table-active">{{ pricing.topMixPrice| number:'1.0-1' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total price</strong></td>
                                <td class="table-active" id="totalPrice"><strong>{{ pricing.totalPrice | number:'1.0-1' }}</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</form>
