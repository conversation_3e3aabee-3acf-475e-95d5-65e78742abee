<div [formGroup]="gfForm" class="row">
    <div class="row" *ngIf="showTitle">
        <label for="gfLowField" class="form-label">Conservatism (Gradient factors):</label>
    </div>
    <div class="mb-4 col col-12" [ngClass]="{ 'col-sm-6': !simple }">
        <div>
            <label *ngIf="!simple" for="gfLowField" class="form-label" mdbLabel>Gradient low [%]:</label>
            <div mdbDropdown class="input-group dropdown" [class.form-outline]="simple">
                <input id="gfLowField" *ngIf="simple" [value]="conservatism" class="form-control" type="text" readonly />
                <input id="gfLowField" *ngIf="!simple" class="form-control" type="number" formControlName="gfLow"
                    (input)="gfLowChanged()" required [min]="minGradient" [max]="maxGradient" step="1" [class.is-invalid]="gfLowInvalid" />
                <button type="button" class="btn btn-default dropdown-toggle dropdown-toggle-split"
                    id="conservatismMenu" mdbDropdownToggle data-mdb-toggle="dropdown" aria-expanded="false"></button>
                <ul class="dropdown-menu dropdown-menu-end" mdbDropdownMenu>
                    <li (click)="lowConservatism()"><a class="dropdown-item">{{standards.lowName}}</a></li>
                    <li (click)="mediumConservatism()"><a class="dropdown-item">{{standards.mediumName}}</a>
                    </li>
                    <li (click)="highConservatism()"><a class="dropdown-item">{{standards.highName}}</a></li>
                </ul>
            </div>
            <div class="text-danger form-text position-absolute" *ngIf="gfLowInvalid">
                Needs to be number {{ minGradient }} - {{ maxGradient }} lower than gradient high</div>
        </div>
    </div>
    <div class="mb-4 col col-12 col-sm-6" *ngIf="!simple">
        <label for="gfHighField" class="form-label" mdbLabel>Gradient high [%]:</label>
        <input id="gfHighField" formControlName="gfHigh" class="form-control" type="number" required
            (input)="gfHighChanged()" [min]="minGradient" [max]="maxGradient" step="1" [class.is-invalid]="gfHighInvalid" />
        <div class="invalid-feedback position-absolute">
            Needs to be number {{ minGradient }} - {{ maxGradient }}  higher than gradient low
        </div>
    </div>
</div>
