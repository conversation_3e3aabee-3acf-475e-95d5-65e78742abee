# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GasPlanner_TS2C is a web-based scuba diving gas planning application built with Angular 19 and TypeScript. It consists of two main projects:
- **planner**: The main Angular application (PWA)
- **scuba-physics**: A TypeScript library containing decompression algorithms and diving calculations

The application uses the Bühlmann ZHL-16C decompression algorithm with gradient factors for dive planning, gas consumption calculations, and various diving-related calculators.

## Common Development Commands

### Build Commands
- `npm run build-lib` - Build the scuba-physics library (required before building the app)
- `npm run build` - Build the production application
- `npm start` - Build library and start development server
- `npm run start-pwa` - Test PWA functionality in production mode

### Testing and Quality
- `npm test` - Run tests for the main application
- `npm run test-lib` - Run tests for the scuba-physics library
- `npm run test-ci` - Run tests in CI mode (headless Chrome, no watch)
- `npm run test-lib-ci` - Run library tests in CI mode
- `npm run lint` - Run ESLint on both projects
- `npm run e2e` - Run end-to-end tests with Playwright

### Development Workflow
1. Always build the library first: `npm run build-lib`
2. Then start development: `npm start` or build: `npm run build`
3. Run linting before committing: `npm run lint`

## Architecture Overview

### Project Structure
- `projects/planner/` - Main Angular application
  - `src/app/shared/` - Core services (PlannerService, Plan, DiveSchedules, etc.)
  - `src/app/calculators/` - Individual calculator components
  - `src/app/plan/` - Main dive planning interface components
  - `src/app/controls/` - Reusable UI controls
  - `src/app/workers/` - Web Workers for heavy calculations
- `projects/scuba-physics/` - Domain library with pure algorithms
  - `src/lib/algorithm/` - Bühlmann decompression algorithm
  - `src/lib/calculators/` - Various diving calculators
  - `src/lib/physics/` - Physics calculations and unit conversions
  - `src/lib/gases/` - Gas mixture calculations
  - `src/lib/consumption/` - Gas consumption calculations
  - `src/lib/depths/` - Depth and segment planning

### Key Architecture Patterns
- **Domain-Driven Design**: Clear separation between UI (planner) and domain logic (scuba-physics)
- **Worker-Based Computing**: Heavy calculations run in Web Workers to maintain UI responsiveness
- **DTO Pattern**: Data transfer objects handle communication between layers
- **Service Layer**: PlannerService orchestrates all dive planning operations

### Core Services
- **PlannerService** (`shared/planner.service.ts`): Main coordinator for dive planning calculations
- **Plan** (`shared/plan.service.ts`): Manages dive segments and plan configuration
- **DiveSchedules** (`shared/dive.schedules.ts`): Handles multiple dive scheduling
- **WorkersFactory** (`workers/`): Creates and manages Web Workers for async calculations

### Data Flow
1. User modifies dive plan → DelayedScheduleService debounces changes
2. PlannerService creates PlanRequestDto from current plan state
3. Profile Worker calculates decompression profile
4. Parallel execution: DiveInfo Worker (NDL, ceiling, etc.) and Consumption Worker (gas usage)
5. Results update DiveResults and trigger UI refresh via ReloadDispatcher

## Development Guidelines

### Working with the Library
- The scuba-physics library must be built before the main application
- Library changes require rebuilding with `npm run build-lib`
- Use the library's public API through the main application's services

### Adding New Features
1. **For calculations**: Add to scuba-physics library, then expose through application services
2. **For UI**: Follow existing component patterns in calculators/ or plan/ directories
3. **For heavy computation**: Consider using Web Workers via WorkersFactory

### Code Style
- Follows Angular and TypeScript conventions
- Uses ESLint with strict Angular rules
- SCSS for styling with component-scoped styles
- Proper TypeScript typing throughout

### PWA Features
- Service worker configuration in `ngsw-config.json`
- Offline functionality for calculations
- Web app manifest for mobile installation

## Documentation
- User documentation is in `doc/` directory and embedded in the application
- Help system renders Markdown files directly in the UI
- API documentation through TypeScript interfaces and JSDoc comments
