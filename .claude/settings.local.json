{"permissions": {"allow": ["Bash(find:*)", "Bash(tree:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(gcc:*)", "Bash(./test_precision_defaults)", "Bash(node:*)", "Bash(npx ts-node:*)", "Bash(npx tsc:*)", "Bash(./c_port/test/common/test-featureFlags:*)", "Bash(./c_port/test/common/test-precision:*)", "Bash(./c_port/test/common/test-linearFunction:*)", "Bash(grep:*)", "Bash(npm run build-lib:*)", "Bash(./test/common/test-binaryIntervalSearch:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(make:*)", "<PERSON><PERSON>(make test:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run-tests.sh)", "mcp__ide__executeCode"], "deny": [], "ask": []}}