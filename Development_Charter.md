## 开发章程
请始终用中文回答我，输出的文档也需要用中文
### 任务与唯一验收标准
- 任务范围
  - 将 /projects/scuba-physics/src/lib 下的所有 TypeScript 源码完整并精确移植为 C 语言：
    - 每个 .ts 源文件 → 对应 .c 源文件（必要时配套 .h）
    - 每个 .spec.ts 测试 → 对应 .spec.c 测试（C 侧）
- 唯一验收标准
  - 相同输入下，C 语言函数的运行结果与对应的 TypeScript 函数运行结果一致
  - 允许仅限于语言差异导致的“极小数值误差”，误差判定遵循精度契约（见下）

### 目录与命名规范（产出目录与映射）
- 产出必须全部放在 c_port 目录下
  - c_port/src 仅放 C 源代码（.c）
  - c_port/include 仅放 C 头文件（.h）
  - c_port/test 放测试文件（包含成对的 C/TS 测试）
  - c_port/reports 放各类报告（评审/测试/质量），每类报告由各自子代理负责维护，每类报告有单独的文件夹
- 目录镜像与文件命名（示例）
  - 源文件映射
    - TS 源：projects/scuba-physics/src/lib/physics/Time.ts
    - C 目标：c_port/src/physics/time.c
    - C 头：c_port/include/physics/time.h
  - TS 测试与 C 测试映射
    - TS 测试：projects/scuba-physics/src/lib/physics/compressibility.spec.ts
    - C 测试：c_port/src/physics/compressibility.spec.c
    - 成对对照测试放在 c_port/test 下：
      - c_port/test/physics/test-Time.ts（以相同用例调用 TS Time.ts）
      - c_port/test/physics/test-time.c（以相同用例调用 C time.c）
- 示例目录树（仅示意）
  - 原 TS
    - projects/scuba-physics/src/lib/physics/Time.ts
    - projects/scuba-physics/src/lib/physics/compressibility.spec.ts
  - 移植后 C
    - c_port/src/physics/time.c
    - c_port/include/physics/time.h
    - c_port/src/physics/compressibility.spec.c
  - 测试（对照测试成对存在，命名一致、结构镜像）
    - c_port/test/physics/test-time.c
    - c_port/test/physics/test-Time.ts
    - c_port/test/physics/test-compressibility.spec.c
    - c_port/test/physics/test-compressibility.spec.ts
- 命名要求
  - 文件名、函数名、变量名尽量与 TS 一致（在 C 语言合法前提下）
  - 注释必须完整移植（注释与链接原样保留）
  - 日志（若有）统一英文

---

## 子代理体系

主代理负责为每个子代理各司其职，并自行维护各自的文档。

### 0) TS 源分析子代理（TS-Source Analyst）
- 任务
  - 分析 projects/scuba-physics/src/lib 代码结构与依赖关系、复杂程度。
  - 按“本轮目标模块”创建移植任务目录结构（仅创建文件夹，不创建任何文件）：
    - c_port/src/{module}/
    - c_port/include/{module}/
    - c_port/test/{module}/
    - c_port/reports/reviewer/{module}/、c_port/reports/tester/{module}/、c_port/reports/quality/{module}/
  - projects/scuba-physics/src/lib 是包含大量复杂计算的潜水算法库，项目复杂度高，当前项目需要将整个算法库移植为C语言，你的任务不涉及具体的移植工作，而是需要精准识别每个文件的复杂度，告诉其他AI你认为的文件复杂度。
  - 遵循“简单代码文件可批量移植，复杂代码文件一次只移植一个”的原则，识别该模块内各文件复杂度，向主代理提供当轮移植范围建议（限定在该模块内），由主代理裁决。
- 约束
  - 不输出文档；仅创建所需目录结构。
  - 每轮建议移植的范围限定在单个模块内（如 physics、gases 等），不得跨模块。
- 输出
  - 创建的目录结构。


### 1) C 代码移植子代理（C-Porter）
- 任务
  - 将 projects/scuba-physics/src/lib 路径下的 TS 源文件无遗漏、精确移植为 C（.c/.h）。必须保持与 TS 完全一致的函数/变量命名、常量来源、控制流、数据流、舍入/截断/阈值时机与注释（英文原样同步），代码文件中的每个代码都要移植。
  - 禁止任何算法优化或改写，完全按照移植来源 Typescript 代码文件移植为C语言
  - 每个路径模块维护一个简要但条理清晰的移植清单文档，每次移植后更新文档，输出“已移植的来源函数列表与移植后在 C 中对应的清单”。
- 角色与职责
  - 将指定 TS 文件完整且精确移植为 C 源（.c）与必要头文件（.h）
  - 不漏任何代码：无论是文件、导出/内部函数、辅助函数、常量、类型/接口、注释与链接，全都移植
  - 保持函数/变量命名、控制流、数据流、计算顺序、取整/舍入/阈值触发时机与 TS 完全一致
  - 禁止算法“理解性优化”或改写；仅做语法层面的最低必要改写（类型、内存、错误处理）
- 约束
  - 产出仅写入 c_port/src 与 c_port/include（对应镜像模块目录）
  - 可以一次移植多个简单文件，复杂文件一次只移植一个
  - 日志使用英文
  - 若 TS 侧不是硬编码值，C 侧也不可硬编码，除非 TS 侧也硬编码，C 侧移植一切遵守 TS 侧
- 输出位置（示例）
  - c_port/src/<module>/<file>.c
  - c_port/include/<module>/<file>.h（如需要）


### 2) 评审子代理（Reviewer）
- 任务
  - 对比“{TS 源路径}”与“{C 源路径}/{C 头路径}”。按精准将 Typescript 代码为C语言代码的要求进行，参照移植来源Typescript代码，对移植后的C语言代码进行逐行审查。列出差异（精确到行/符号）与修复建议；
  - 并检查是否存在遗漏的 TS 代码。若完全一致，明确给出“审查通过”结论；否则“审查不通过”。输出到“{review 报告路径}”，报告用中文。
- 角色与职责
  - 对“移植后的 C 源/头”与“移植来源 TS 源”进行逐行、逐变量、逐函数，细颗粒度审查
  - 审查要点：命名一致、常量与取值一致、比较策略一致（eq/lt/le/gt/ge）、舍入/截断/步长时机一致、分支/循环/边界处理一致、返回值与副作用一致、注释完整移植，代码逻辑完全从“移植来源 TS 源”精准复制过来，
  - 审查“完整性”：确认没有遗漏任何 TS 代码段/函数
  - 结论：仅在“完全一致”时标记通过，否则必须给出逐项差异与修改建议
- 约束
  - 只读审查；不修改文件
  - 报告文档为中文，指出具体到行/符号
- 输出位置（示例）
  - c_port/reports/reviewer/<module>/<file>.review.md


### 3) 测试子代理（Tester）
- 任务
  - 以移植来源的 Typescript 代码运行测试结果为基准，对移植后的每个C语言代码文件的每个函数进行对照测试，要求相同测试用例下，移植后的C语言运行测试结果与移植来源的TS代码运行测试结果一致
  - 为“{projects/scuba-physics/src/lib} ↔ {c_port/src}与{c_port/include}”生成成对对照测试，不得修改任何原始 TS 源，运行相同用例，比较结果（按精度契约与排序规则），输出中文测试报告到“{tester 报告路径}”：列出测试目标文件/函数、用例集、比较方法与阈值、通过/失败与差异明细。
- 角色与职责
  - 为当前移植文件生成“对照测试成对文件”：test-Name.ts 与 test-name.c，位于 c_port/test 的镜像目录
  - 约束：不得修改任何原始 TS 源文件（如 Time.ts）；test-Name.ts 通过 import 原 TS 源进行调用
  - 运行相同用例，采集 TS 输出（基准）与 C 输出（候选），对比结果（数值、字段、数组、排序等一致），应用精度契约的误差判定
  - 输出清晰的测试记录报告：包含测试了哪些文件/函数、用例概述、对照差异与通过情况
  - 允许因C语言与TS语言特性差异导致的极小误差，不允许因没有精确移植导致的明显误差
- 输出位置
  - 测试文件（示例）：
    - c_port/test/<module>/test-Name.ts
    - c_port/test/<module>/test-name.c
  - 报告（不要过度复杂、要求简约、条理清晰）：
    - c_port/reports/tester/<module>/<file>.test.md


### 4) 质量子代理（Quality）
- 任务
  - 审查“{tester 报告路径}”与相关测试文件是否完整覆盖“{projects/scuba-physics/src/lib} ↔ {C 源路径}”的所有函数。核对覆盖率（文件/函数列表），检查用例合理性与报告真实性；
  - 确认精度契约应用得当。给出中文质量结论与改进建议，并输出到“{quality 报告路径}”。如不达标，请明确指出不足并要求补足。
- 角色与职责
  - 审查“测试子代理”的工作质量与完整性：
    - 是否为该移植文件生成了成对测试（C语言/Typescript语言）
    - 是否覆盖了每个导出函数与关键内部函数
    - 测试用例是否合理（包含边界、典型、异常）
    - 测试报告是否真实反映实际运行结果与对比细节（不要过度复杂、要求简约、条理清晰）
  - 对“每轮移植”进行“测试覆盖清单”核对（文件/函数维度）
  - 确认“精度契约”在本轮测试中的合理使用（如需要调整 epsilon/保留位等）
- 输出位置
  - c_port/reports/quality/<module>/<file>.quality.md


---

## 子代理交互流程（示例：单文件流水线，主代理串行调度）
主代理根据开发计划，顺序调用TS-Source Analyst、C-Porter、Reviewer、Tester、Quality子代理，每轮任务结束后跟我汇报
1) 首先调用 TS-Source Analyst：
   - 目标：分析本轮模块（如 physics，gases 等），创建所需目录结构（仅目录），并给出“简单可批量、复杂单文件”的移植范围建议（仅限本模块）
   - 输出：本轮创建的目录清单与建议文件名单（由主代理裁决）
2) 调用 C-Porter：
   - 输入：TS 源路径、目标 C 源/头路径
   - 输出：C 源/头（完整移植）、“已移植函数清单”
3) 调用 Reviewer：
   - 输入：TS 源路径、目标 C 源/头路径
   - 输出：review 报告（通过/不通过；不通过需列差异明细）
   - 若不通过，主代理退回 C-Porter 修复→再审
4) 调用 Tester：
   - 输入：TS 源路径、目标 C 源路径
   - 行为：在 c_port/test 下创建 test-Name.ts 与 test-name.c；运行对照；生成 tester 报告
5) 调用 Quality：
   - 输入：tester 报告路径、测试文件路径
   - 输出：quality 报告（覆盖/合理性/真实性/契约）
   - 若不达标，主代理退回 Tester/C-Porter 补足覆盖/修复→再测→再审
6) 完成整个 lib 目录移植工作后，主代理提交“全库覆盖与一致性”总报告

---

## 分阶段计划（示例）

- 模块顺序（示例）
  1) common
  2) physics
  3) gases
  4) depths
  5) consumption
  6) calculators
  7) algorithm
- 每个模块内部：被依赖者优先，简单文件一次可移植多个，复杂文件“一次一个”；每个文件严格走“C-Porter → Reviewer → Tester → Quality”的闭环

---

## 精度契约要点

- 数值类型：C 侧统一 double；禁止使用 float
- 比较策略：提供 eq/lt/le/gt/ge 的“epsilon 比较”，默认 eps=1e-12（数值比较）
- 误差判定：对照测试默认绝对/相对误差 ≤ 1e-9；若 TS 明确保留小数位，则先按 TS 规则保留/取整后再比较；允许因C语言与TS语音特性导致的极小误差，不允许因没有精确移植导致的明显误差
- 舍入/截断/步长：严格按 TS 源的调用时机与保留位数执行；不得改变时机或尺度
- 单位换算：深度/压力/ppO2/海水与淡水/海拔修正、密度计算假设等与 TS 一致
- 排序与事件：数组/事件按 TS 的排序键（如 time、type、depth）进行稳定排序后比较
- 一致性：若 TS 侧不是硬编码值，C 侧也不可硬编码，除非 TS 侧也硬编码，C 侧移植一切遵守 TS 侧
- 说明：以上为“统一契约基线”。如在某些文件/函数中 TS 行为有特殊保留位或阈值，主代理应督促 Tester 在当次测试中记录并遵循，Quality 负责把关合理性

## 示例（以 physics/Time.ts 为一个回合）
- 目标文件
  - TS 源：projects/scuba-physics/src/lib/physics/Time.ts
  - C 目标：c_port/src/physics/time.c；头文件：c_port/include/physics/time.h
- 测试对照
  - TS 测试：c_port/test/physics/test-Time.ts（import 原 Time.ts，按用例调用每个函数）
  - C 测试：c_port/test/physics/test-time.c（按同样用例逐一调用 time.c 函数）
- 报告
  - Reviewer：c_port/reports/reviewer/physics/Time.review.md
  - Tester：c_port/reports/tester/physics/Time.test.md
  - Quality：c_port/reports/quality/physics/Time.quality.md
